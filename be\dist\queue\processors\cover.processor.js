"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CoverProcessor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoverProcessor = void 0;
const bull_1 = require("@nestjs/bull");
const common_1 = require("@nestjs/common");
const constant_1 = require("../constant");
const music_service_1 = require("../../music/music.service");
const polling_service_1 = require("../polling.service");
let CoverProcessor = CoverProcessor_1 = class CoverProcessor {
    constructor(musicService, pollingService) {
        this.musicService = musicService;
        this.pollingService = pollingService;
        this.logger = new common_1.Logger(CoverProcessor_1.name);
        this.logger.log('QueueProcessor initialized');
    }
    async handle(job) {
        try {
            await job.log(`Generating music cover for music: ${job.data.musicId}`);
            const coverTaskId = await this.musicService.generateMusicCover(job.data.musicId);
            await job.log(`Polling music cover status for music: ${job.data.musicId}, coverTaskId: ${coverTaskId}`);
            await this.pollingService.startPollingCoverStatus({
                musicId: job.data.musicId,
                taskId: coverTaskId,
                userId: job.data.userId
            });
            return {
                taskId: coverTaskId,
                musicId: job.data.musicId,
                userId: job.data.userId,
            };
        }
        catch (error) {
            this.logger.error(error.message, error.stack);
            throw new Error(error.message);
        }
    }
    onActive(job) {
        this.logger.log(`Processing job ${job.id} of type ${job.name}`);
    }
    onCompleted(job, result) {
        this.logger.log(`Job ${job.id} completed with result: ${JSON.stringify(result)}`);
    }
    onFailed(job, err) {
        this.logger.error(`Job ${job.id} failed with error: ${err.message}`, err.stack);
    }
    onError(err) {
        this.logger.error(`Queue error: ${err.message}`, err.stack);
    }
};
exports.CoverProcessor = CoverProcessor;
__decorate([
    (0, bull_1.Process)(constant_1.QUEUE_GENERATE_COVER_JOB),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CoverProcessor.prototype, "handle", null);
__decorate([
    (0, bull_1.OnQueueActive)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], CoverProcessor.prototype, "onActive", null);
__decorate([
    (0, bull_1.OnQueueCompleted)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], CoverProcessor.prototype, "onCompleted", null);
__decorate([
    (0, bull_1.OnQueueFailed)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Error]),
    __metadata("design:returntype", void 0)
], CoverProcessor.prototype, "onFailed", null);
__decorate([
    (0, bull_1.OnQueueError)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Error]),
    __metadata("design:returntype", void 0)
], CoverProcessor.prototype, "onError", null);
exports.CoverProcessor = CoverProcessor = CoverProcessor_1 = __decorate([
    (0, bull_1.Processor)(constant_1.QUEUE_GENERATE_COVER),
    __metadata("design:paramtypes", [music_service_1.MusicService,
        polling_service_1.PollingService])
], CoverProcessor);
//# sourceMappingURL=cover.processor.js.map