"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var QueueService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueueService = void 0;
const common_1 = require("@nestjs/common");
const bull_1 = require("@nestjs/bull");
const constant_1 = require("./constant");
const event_emitter_1 = require("@nestjs/event-emitter");
let QueueService = QueueService_1 = class QueueService {
    constructor(musicQueue, coverQueue, fileQueue, eventEmitter) {
        this.musicQueue = musicQueue;
        this.coverQueue = coverQueue;
        this.fileQueue = fileQueue;
        this.eventEmitter = eventEmitter;
        this.logger = new common_1.Logger(QueueService_1.name);
    }
    onModuleInit() {
        this.eventEmitter.on('music.generate.cover', (data) => {
            this.generateCover(data);
        });
        this.eventEmitter.on('music.generate.music', (data) => {
            this.generateMusic(data);
        });
        this.eventEmitter.on('music.upload.audio', (data) => {
            this.uploadAudio(data);
        });
        this.eventEmitter.on('music.upload.cover', (data) => {
            this.uploadCover(data);
        });
    }
    async generateMusic(data) {
        const job = await this.musicQueue.add(constant_1.QUEUE_GENERATE_MUSIC_JOB, data);
        this.logger.log(`Job ${job.id} added to queue: ${JSON.stringify(data)}`);
        return job;
    }
    async generateCover(data) {
        const job = await this.coverQueue.add(constant_1.QUEUE_GENERATE_COVER_JOB, data);
        this.logger.log(`Job ${job.id} added to queue: ${JSON.stringify(data)}`);
        return job;
    }
    async uploadAudio(data) {
        const job = await this.fileQueue.add(constant_1.QUEUE_UPLOAD_AUDIO_JOB, data);
        this.logger.log(`Job ${job.id} added to queue: ${JSON.stringify(data)}`);
        return job;
    }
    async uploadCover(data) {
        const job = await this.fileQueue.add(constant_1.QUEUE_UPLOAD_COVER_JOB, data);
        this.logger.log(`Job ${job.id} added to queue: ${JSON.stringify(data)}`);
        return job;
    }
};
exports.QueueService = QueueService;
exports.QueueService = QueueService = QueueService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, bull_1.InjectQueue)(constant_1.QUEUE_GENERATE_MUSIC)),
    __param(1, (0, bull_1.InjectQueue)(constant_1.QUEUE_GENERATE_COVER)),
    __param(2, (0, bull_1.InjectQueue)(constant_1.QUEUE_UPLOAD_FILE)),
    __metadata("design:paramtypes", [Object, Object, Object, event_emitter_1.EventEmitter2])
], QueueService);
//# sourceMappingURL=queue.service.js.map