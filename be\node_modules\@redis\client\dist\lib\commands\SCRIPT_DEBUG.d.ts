import { CommandParser } from '../client/parser';
import { SimpleStringReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the SCRIPT DEBUG command
     *
     * @param parser - The command parser
     * @param mode - Debug mode: YES, SYNC, or NO
     * @see https://redis.io/commands/script-debug/
     */
    readonly parseCommand: (this: void, parser: CommandParser, mode: 'YES' | 'SYNC' | 'NO') => void;
    readonly transformReply: () => SimpleStringReply<'OK'>;
};
export default _default;
//# sourceMappingURL=SCRIPT_DEBUG.d.ts.map