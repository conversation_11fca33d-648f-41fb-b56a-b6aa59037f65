export interface Track {
  id: string;
  title: string;
  audioUrl: string;
  imageUrl: string;
  imageLargeUrl: string;
  duration: number;
}

export interface Music {
  _id: string;
  title: string;
  prompt: string;
  status: "PENDING" | "GENERATING_LYRICS" | "LYRICS_GENERATED" | "GENERATING_MUSIC" | "MUSIC_GENERATED" | "FIRST_TRACK_GENERATED" | "GENERATING_COVER" | "COVER_GENERATED" | "SUCCESS" | "FAILED" | "TEXT_SUCCESS" | "FIRST_SUCCESS" | "GENERATE_AUDIO_FAILED" | "CREATE_TASK_FAILED" | "pending" | "completed" | "failed" | "succeeded";
  audioUrl?: string;
  cover?: string;
  lyrics?: string;
  createdAt: string;
  tags?: string;
  type?: string;
  visibility?: "public" | "private";
  coverGenerating?: boolean;
  metadata?: {
    duration?: number;
    created_at?: string;
    model_name?: string;
    model_version?: string;
    style?: string;
    error?: string;
    error_detail?: string;
  };
  sharedWith?: Array<string>;
  clips?: Array<{
    id: string;
    title: string;
    audio_url: string;
    video_url: string;
    image_url: string;
    image_large_url: string;
    duration: number;
    model_name: string;
    status: string;
    created_at: string;
    lyrics: string;
    style: string;
    prompt: string;
  }>;
  tracks?: Track[];
  userId?: User | {
    _id: string;
    id: string;
    name: string;
  };
  negativePrompt?: string;
  instrumental?: boolean;
  isCustomMode?: boolean;
  updatedAt?: string;
}

export interface User {
  _id: string;
  name: string;
  author_total_music_count?: number;
  whatsapp?: string;
  email?: string;
  credits?: number;
}

export interface GenerateCoverData {
  status: string;
  progress: number;
  result: string;
}

export interface GenerateCoverResponse {
  message: string;
  data: GenerateCoverData;
  error?: string;
}
export interface MusicClip {
  id: string;
  title: string;
  audio_url: string;
  video_url: string;
  image_url: string;
  image_large_url: string;
  duration: number;
  model_name: string;
  status: string;
  created_at: string;
  lyrics: string;
  style: string;
  prompt: string;
}
