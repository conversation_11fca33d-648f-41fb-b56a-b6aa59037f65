import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { musicService } from "../services/api";
import { Music } from "../types";
import Loader from "../components/Loader";

export default function Dashboard() {
  const [recentMusic, setRecentMusic] = useState<Music[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    total: 0,
    completed: 0,
    pending: 0,
    failed: 0,
  });

  useEffect(() => {
    const loadDashboard = async () => {
      try {
        const musics = (await musicService.listMusic()).data.userMusics;
        const recent = musics.slice(0, 3); // Only show 3 most recent
        setRecentMusic(recent);
  
        // Calculate stats
        setStats({
          total: musics.length,
          completed: musics.filter((m: Music) => ['SUCCESS', 'completed', 'succeeded'].includes(m.status.toUpperCase())).length,
          pending: musics.filter((m: Music) => ['PENDING', 'GENERATING_LYRICS', 'LYRICS_GENERATED', 'GENERATING_MUSIC', 'MUSIC_GENERATED', 'FIRST_TRACK_GENERATED', 'GENERATING_COVER', 'COVER_GENERATED', 'TEXT_SUCCESS', 'FIRST_SUCCESS', 'pending'].includes(m.status.toUpperCase())).length,
          failed: musics.filter((m: Music) => ['FAILED', 'GENERATE_AUDIO_FAILED', 'CREATE_TASK_FAILED', 'failed'].includes(m.status.toUpperCase())).length,
        });
      } catch (error) {
        console.error("Error loading dashboard:", error);
      } finally {
        setLoading(false);
      }
    };
  
    loadDashboard();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-[calc(100vh-2rem)]">
        <Loader size="large" />
      </div>
    );
  }

  return (
    <div className="container-custom p-4 lg:p-8 max-w-7xl mx-auto">
      {/* Header Section */}
      <div className="mb-6 lg:mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
          <div className="flex items-center gap-3">
            <div className="p-2 sm:p-3 bg-primary/10 rounded-lg">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 sm:h-8 sm:w-8 text-primary"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
              </svg>
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold">
                Selamat Datang!
              </h1>
              <p className="text-sm sm:text-base text-base-content/60">
                Lihat ringkasan aktivitas musikmu di sini
              </p>
            </div>
          </div>
          <Link to="/generate" className="hidden sm:flex btn btn-primary">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="w-5 h-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                clipRule="evenodd"
              />
            </svg>
            Buat Musik Baru
          </Link>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6 mb-6 lg:mb-8">
        <div className="bg-base-100 border border-base-200 rounded-lg p-3 sm:p-4 lg:p-6">
          <p className="text-xs sm:text-sm text-base-content/60 mb-1">
            Total Musik
          </p>
          <h3 className="text-xl sm:text-2xl lg:text-3xl font-bold">
            {stats.total}
          </h3>
        </div>
        <div className="bg-base-100 border border-base-200 rounded-lg p-3 sm:p-4 lg:p-6">
          <p className="text-xs sm:text-sm text-base-content/60 mb-1">
            Selesai
          </p>
          <h3 className="text-xl sm:text-2xl lg:text-3xl font-bold text-success">
            {stats.completed}
          </h3>
        </div>
        <div className="bg-base-100 border border-base-200 rounded-lg p-3 sm:p-4 lg:p-6">
          <p className="text-xs sm:text-sm text-base-content/60 mb-1">
            Sedang Diproses
          </p>
          <h3 className="text-xl sm:text-2xl lg:text-3xl font-bold text-warning">
            {stats.pending}
          </h3>
        </div>
        <div className="bg-base-100 border border-base-200 rounded-lg p-3 sm:p-4 lg:p-6">
          <p className="text-xs sm:text-sm text-base-content/60 mb-1">Gagal</p>
          <h3 className="text-xl sm:text-2xl lg:text-3xl font-bold text-error">
            {stats.failed}
          </h3>
        </div>
      </div>

      {/* Mobile New Music Button */}
      <div className="sm:hidden mb-6">
        <Link to="/generate" className="btn btn-primary w-full">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="w-5 h-5"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
              clipRule="evenodd"
            />
          </svg>
          Buat Musik Baru
        </Link>
      </div>

      {/* Recent Music */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg sm:text-xl lg:text-2xl font-bold">
            Musik Terbaru
          </h2>
          <Link
            to="/history"
            className="text-xs sm:text-sm text-base-content/60 hover:text-base-content"
          >
            Lihat Semua →
          </Link>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6">
          {recentMusic.length > 0 ? (
            recentMusic.map((music) => (
              <div
                key={music._id}
                className="bg-base-100 border border-base-200 rounded-lg p-4"
              >
                <div className="flex justify-between items-start gap-3 mb-3">
                  <h3 className="font-medium line-clamp-1 flex-1">
                    {music.title}
                  </h3>
                  <div className="badge badge-sm">
                    {['SUCCESS', 'completed', 'succeeded'].includes(music.status.toUpperCase()) ? 'Selesai' :
                      ['FAILED', 'GENERATE_AUDIO_FAILED', 'CREATE_TASK_FAILED', 'failed'].includes(music.status.toUpperCase()) ? 'Gagal' :
                        <div className="flex items-center gap-2">
                          <span>
                            {music.status.toUpperCase() === 'PENDING' ? 'Menunggu' :
                             music.status.toUpperCase() === 'GENERATING_LYRICS' ? 'Membuat Lirik' :
                             music.status.toUpperCase() === 'LYRICS_GENERATED' ? 'Lirik Selesai' :
                             music.status.toUpperCase() === 'GENERATING_MUSIC' ? 'Membuat Audio' :
                             music.status.toUpperCase() === 'MUSIC_GENERATED' ? 'Audio Selesai' :
                             music.status.toUpperCase() === 'FIRST_TRACK_GENERATED' ? 'Track Pertama Selesai' :
                             music.status.toUpperCase() === 'GENERATING_COVER' ? 'Membuat Cover' :
                             music.status.toUpperCase() === 'COVER_GENERATED' ? 'Cover Selesai' :
                             music.status.toUpperCase() === 'TEXT_SUCCESS' ? 'Membuat Lirik' :
                             music.status.toUpperCase() === 'FIRST_SUCCESS' ? 'Membuat Audio' :
                             'Sedang Diproses'}
                          </span>
                          <Loader size="small" />
                        </div>
                    }
                  </div>
                </div>
                <p className="text-sm text-base-content/60 mb-3 line-clamp-2">
                  {music.prompt}
                </p>
                <div className="flex justify-between items-center text-xs sm:text-sm text-base-content/60">
                  <span>
                    {new Date(music.createdAt).toLocaleDateString("id-ID")}
                  </span>
                  <Link
                    to={`/history?id=${music._id}`}
                    className="hover:text-base-content"
                  >
                    Lihat Detail →
                  </Link>
                </div>
              </div>
            ))
          ) : (
            <div className="col-span-1 sm:col-span-2 lg:col-span-3 text-center py-8 bg-base-100 border border-base-200 rounded-lg">
              <p className="text-base-content/60 mb-2">
                Belum ada musik yang dibuat
              </p>
              <Link
                to="/generate"
                className="text-primary hover:underline inline-block text-sm"
              >
                Buat Musik Pertama →
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
