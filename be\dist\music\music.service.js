"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var MusicService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MusicService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const music_schema_1 = require("./schemas/music.schema");
const users_service_1 = require("../users/users.service");
const event_emitter_1 = require("@nestjs/event-emitter");
const music_creator_service_1 = require("./music-creator.service");
let MusicService = MusicService_1 = class MusicService {
    constructor(musicModel, usersService, eventEmitter, musicCreatorService) {
        this.musicModel = musicModel;
        this.usersService = usersService;
        this.eventEmitter = eventEmitter;
        this.musicCreatorService = musicCreatorService;
        this.logger = new common_1.Logger(MusicService_1.name);
    }
    async syncAllMusicSchema() {
        try {
            const musics = await this.musicModel.find({});
            let total = 0;
            for (const music of musics) {
                music.status = 'SUCCESS';
                if (music.tracks.length > 0 && music.userId) {
                    total++;
                    const clips = [];
                    clips.push({
                        _id: new mongoose_2.default.Types.ObjectId(),
                        id: new mongoose_2.default.Types.ObjectId().toHexString(),
                        title: music.tracks[0].title,
                        audio_url: music.tracks[0].audioUrl,
                        video_url: null,
                        image_url: music.tracks[0].imageUrl,
                        image_large_url: music.tracks[0].imageUrl,
                        duration: music.tracks[0].duration,
                        model_name: music.tracks[0].modelName,
                        status: 'SUCCESS',
                        created_at: music.tracks[0].createdAt,
                        lyrics: music.tracks[0].prompt,
                        style: music.tracks[0].tags,
                        prompt: music.tracks[0].prompt
                    });
                    clips.push({
                        _id: new mongoose_2.default.Types.ObjectId(),
                        id: new mongoose_2.default.Types.ObjectId().toHexString(),
                        title: music.tracks[1].title,
                        audio_url: music.tracks[1].audioUrl,
                        video_url: null,
                        image_url: music.tracks[1].imageUrl,
                        image_large_url: music.tracks[1].imageUrl,
                        duration: music.tracks[1].duration,
                        model_name: music.tracks[1].modelName,
                        status: 'SUCCESS',
                        created_at: music.tracks[1].createdAt,
                        lyrics: music.tracks[1].prompt,
                        style: music.tracks[1].tags,
                        prompt: music.tracks[1].prompt
                    });
                    music.clips = clips;
                }
                if (music.userId) {
                    await music.save();
                }
            }
            this.logger.log('New clip:', total);
        }
        catch (error) {
            this.logger.error('Error syncing music schema:', error);
            throw new Error(error);
        }
    }
    async getMusic(id, userId) {
        try {
            const music = await this.musicModel.findOne({
                $or: [
                    { _id: id, userId: userId },
                    { _id: id, visibility: 'public' },
                ],
            }).populate('userId', 'name');
            if (!music) {
                throw new common_1.NotFoundException('Music not found');
            }
            return music;
        }
        catch (error) {
            throw new common_1.BadRequestException(error.message);
        }
    }
    async listMusic(userId) {
        if (!userId) {
            throw new common_1.BadRequestException('userId is required');
        }
        const userMusics = await this.musicModel
            .find({ userId })
            .sort({ createdAt: -1 })
            .populate('userId', 'name ');
        const sharedMusics = await this.musicModel
            .find({
            $or: [
                { visibility: 'public' },
            ],
        })
            .sort({ createdAt: -1 })
            .populate('userId', 'name ');
        return {
            userMusics,
            sharedMusics,
        };
    }
    async generateLyrics(prompt) {
        try {
            return await this.musicCreatorService.generateLyrics(prompt);
        }
        catch (error) {
            this.logger.error('Error generating lyrics:', error);
            throw new common_1.BadRequestException('Failed to generate lyrics. Please try again.');
        }
    }
    async getMusicShareData(musicId, userId) {
        const music = await this.musicModel
            .findOne({
            _id: musicId,
            userId: userId,
        })
            .select('_id sharedWith title visibility')
            .populate('sharedWith', 'name whatsapp')
            .exec();
        if (!music) {
            throw new common_1.NotFoundException('Music not found');
        }
        return music;
    }
    async saveMusicShareData(musicId, userId, payload) {
        const music = await this.musicModel
            .findOne({
            userId: userId,
            _id: musicId,
        })
            .exec();
        if (!music) {
            throw new common_1.NotFoundException('Music not found');
        }
        music.sharedWith = payload.shared_with.map((id) => new mongoose_2.default.Types.ObjectId(id));
        music.visibility = payload.visibility;
        await music.save();
        return music;
    }
    async getMusicById(id) {
        return await this.musicModel.findById(id);
    }
    async updateGeneratedCover(musicId, coverUrl) {
        await this.musicModel.findByIdAndUpdate(musicId, { cover: coverUrl }, { new: true });
    }
    async generateMusicQueue(data) {
        try {
            const user = await this.usersService.findOne(data.userId);
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            if (user.credits <= 0) {
                throw new common_1.BadRequestException('Insufficient credits. Please purchase more credits to generate music.');
            }
            const createdMusic = new this.musicModel({
                ...data,
                userId: new mongoose_2.default.Types.ObjectId(data.userId),
                status: 'PENDING'
            });
            const savedMusic = await createdMusic.save();
            this.logger.log(`Music document created with ID: ${savedMusic._id.toString()}`);
            await this.usersService.deductCredits(data.userId, 1);
            this.eventEmitter.emit('music.generate.music', {
                musicId: savedMusic._id.toString(),
                userId: data.userId
            });
            return savedMusic;
        }
        catch (error) {
            this.logger.error('Error generating music queue:', error);
            throw new common_1.InternalServerErrorException('Error generating music queue');
        }
    }
    async generateMusic(musicId) {
        try {
            const music = await this.musicModel.findOne({
                _id: new mongoose_2.default.Types.ObjectId(musicId),
                status: 'PENDING'
            });
            if (!music) {
                throw new common_1.NotFoundException('Music not found');
            }
            const payload = {
                title: music.title,
                prompt: music.prompt,
                userId: music.userId.toString(),
                isCustomMode: music.isCustomMode || false,
                instrumental: music.instrumental,
                negativeTags: music.negativePrompt || '',
                model: 'V4_5',
            };
            const generatedMusic = await this.musicCreatorService.generateMusic(payload);
            if (!generatedMusic) {
                throw new common_1.BadRequestException('No task ID received from SunoAPI');
            }
            return generatedMusic.taskId;
        }
        catch (error) {
            this.logger.error('Error creating music:', error.message);
            throw new common_1.InternalServerErrorException('Error creating music');
        }
    }
    async getMusicStatus(taskId) {
        try {
            const music = await this.musicModel.findOne({
                taskId,
                status: { $ne: music_schema_1.MusicStatus.SUCCESS }
            });
            if (!music) {
                throw new common_1.NotFoundException('Music not found');
            }
            const result = await this.musicCreatorService.getMusicStatus(taskId);
            switch (result.status) {
                case 'PENDING':
                    this.eventEmitter.emit('music.status', {
                        musicId: music._id.toString(),
                        userId: music.userId.toString(),
                        status: music_schema_1.MusicStatus.GENERATING_MUSIC,
                        text: 'Your music generation is in progress...'
                    });
                    break;
                case 'TEXT_SUCCESS':
                    this.logger.log('Lyrics generated successfully');
                    await this.musicModel.findByIdAndUpdate(music._id, {
                        lyrics: result.data[0].prompt
                    });
                    this.eventEmitter.emit('music.status', {
                        musicId: music._id.toString(),
                        userId: music.userId.toString(),
                        status: music_schema_1.MusicStatus.LYRICS_GENERATED,
                        text: 'Lyrics generated successfully, now creating music...'
                    });
                    break;
                case 'FIRST_SUCCESS':
                    this.logger.log('Track 1 created successfully');
                    this.eventEmitter.emit('music.status', {
                        musicId: music._id.toString(),
                        userId: music.userId.toString(),
                        status: music_schema_1.MusicStatus.FIRST_TRACK_GENERATED,
                        text: 'Track 1 created successfully, now creating track 2...'
                    });
                    break;
                case 'SUCCESS':
                    this.eventEmitter.emit('music.status', {
                        musicId: music._id.toString(),
                        userId: music.userId.toString(),
                        status: music_schema_1.MusicStatus.MUSIC_GENERATED,
                        text: 'Music generated successfully!'
                    });
                    result.data.map((track) => {
                        music.tracks.push({
                            id: track.id,
                            audioUrl: track.audioUrl,
                            imageUrl: track.imageUrl,
                            imageLargeUrl: track.imageUrl,
                            prompt: track.prompt,
                            modelName: track.modelName,
                            title: track.title,
                            tags: track.tags,
                            duration: track.duration
                        });
                        music.clips.push({
                            id: track.id,
                            audio_url: track.audioUrl,
                            video_url: null,
                            image_url: track.imageUrl,
                            image_large_url: track.imageUrl,
                            title: track.title,
                            duration: track.duration
                        });
                        this.eventEmitter.emit('music.upload.audio', {
                            musicId: music._id.toString(),
                            fileId: track.id,
                            fileUrl: track.audioUrl
                        });
                        this.eventEmitter.emit('music.upload.cover', {
                            musicId: music._id.toString(),
                            fileId: track.id,
                            fileUrl: track.imageUrl
                        });
                    });
                    music.status = music_schema_1.MusicStatus.SUCCESS;
                    await music.save();
                    this.eventEmitter.emit('music.generate.cover', {
                        musicId: music._id.toString(),
                        userId: music.userId.toString(),
                        taskId: music.taskId
                    });
                    break;
                case 'CREATE_TASK_FAILED':
                case 'GENERATE_AUDIO_FAILED':
                case 'CALLBACK_EXCEPTION':
                case 'SENSITIVE_WORD_ERROR':
                    this.eventEmitter.emit('music.status', {
                        musicId: music._id.toString(),
                        userId: music.userId.toString(),
                        status: music_schema_1.MusicStatus.FAILED,
                        text: 'Failed to create music task'
                    });
                    throw new common_1.InternalServerErrorException('Failed to create music task');
                default:
                    console.log(`Unknown status: ${status}, continuing to poll...`);
                    break;
            }
            return result;
        }
        catch (error) {
            this.logger.error('Error getting music status:', error.message, error.stack);
            throw new common_1.InternalServerErrorException('Error getting music status');
        }
    }
    async assignTaskId(musicId, taskId) {
        try {
            await this.musicModel.findByIdAndUpdate(musicId, {
                taskId,
                status: music_schema_1.MusicStatus.GENERATING_MUSIC
            });
        }
        catch (error) {
            this.logger.error('Error assigning task ID:', error);
            throw new common_1.InternalServerErrorException('Error assigning task ID');
        }
    }
    async generateMusicCover(musicId) {
        try {
            const music = await this.musicModel.findOne({
                _id: new mongoose_2.default.Types.ObjectId(musicId),
                taskId: { $ne: null }
            });
            if (!music) {
                throw new common_1.NotFoundException('Music not found');
            }
            const result = await this.musicCreatorService.generateCover({
                taskId: music.taskId,
            });
            music.coverTaskId = result.taskId;
            await music.save();
            return result.taskId;
        }
        catch (error) {
            this.logger.error('Error generating music cover:', error);
            throw new common_1.InternalServerErrorException('Error generating music cover');
        }
    }
    async getCoverStatus(coverTaskId) {
        try {
            const music = await this.musicModel.findOne({
                coverTaskId
            });
            if (!music) {
                throw new common_1.NotFoundException('Music not found');
            }
            const result = await this.musicCreatorService.getCoverStatus(music.coverTaskId);
            if (result.success && result.images) {
                music.tracks.forEach((track, index) => {
                    if (result.images[index]) {
                        this.eventEmitter.emit('music.upload.cover', {
                            musicId: music._id.toString(),
                            fileId: track.id,
                            fileUrl: result.images[index]
                        });
                    }
                });
            }
            return result;
        }
        catch (error) {
            this.logger.error('Error getting cover status:', error);
            throw new common_1.InternalServerErrorException('Error getting cover status');
        }
    }
    async updateAudioUrl(musicId, fileId, fileUrl) {
        try {
            await this.musicModel.findByIdAndUpdate(musicId, {
                audioUrl: fileUrl
            });
            await this.musicModel.updateOne({
                _id: musicId,
                'tracks.id': fileId
            }, {
                $set: {
                    'tracks.$.audioUrl': fileUrl
                }
            });
        }
        catch (error) {
            this.logger.error('Error updating audio URL:', error);
            throw new common_1.InternalServerErrorException('Error updating audio URL');
        }
    }
    async updateCoverUrl(musicId, fileId, fileUrl) {
        try {
            await this.musicModel.findByIdAndUpdate(musicId, {
                cover: fileUrl
            });
            await this.musicModel.updateOne({
                _id: musicId,
                'tracks.id': fileId
            }, {
                $set: {
                    'tracks.$.imageUrl': fileUrl
                }
            });
        }
        catch (error) {
            this.logger.error('Error updating cover URL:', error);
            throw new common_1.InternalServerErrorException('Error updating cover URL');
        }
    }
};
exports.MusicService = MusicService;
exports.MusicService = MusicService = MusicService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(music_schema_1.Music.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        users_service_1.UsersService,
        event_emitter_1.EventEmitter2,
        music_creator_service_1.MusicCreatorService])
], MusicService);
//# sourceMappingURL=music.service.js.map