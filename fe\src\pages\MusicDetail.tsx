import { useState, useEffect, useRef } from "react";
import { useParams, useNavigate } from "react-router-dom";
import defaultCoverImage from "../assets/wood-blog-placeholder.jpg";
import { musicService } from "../services/api";
import Loader from "../components/Loader";
import { useMusicPlayer } from "../contexts/MusicPlayerContext";
import { Music, MusicClip } from "../types";
import { API_URL } from "../config/api";
import { ShareMusicModal } from "../components/ShareMusicModal";

// Music Player Component
interface MusicPlayerProps {
  audioUrl: string;
  onVersionChange?: (version: number) => void;
  versions?: number;
  currentVersion?: number;
}

const MusicPlayer = ({ audioUrl, onVersionChange, versions = 1, currentVersion = 1 }: MusicPlayerProps) => {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleLoadedMetadata = () => {
      setDuration(audio.duration);
    };

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setCurrentTime(0);
    };

    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('ended', handleEnded);

    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('ended', handleEnded);
    };
  }, [audioUrl]);

  // Reset player when audio URL changes
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.load();
      setCurrentTime(0);
      setIsPlaying(false);
    }
  }, [audioUrl]);

  const togglePlay = () => {
    if (!audioRef.current) return;

    if (isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!audioRef.current) return;
    const time = Number(e.target.value);
    audioRef.current.currentTime = time;
    setCurrentTime(time);
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!audioRef.current) return;
    const vol = Number(e.target.value);
    audioRef.current.volume = vol;
    setVolume(vol);
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="bg-base-100 border border-base-200 rounded-2xl p-6 shadow-lg">
      <audio ref={audioRef} src={audioUrl} preload="metadata" />

      {/* Progress Bar */}
      <div className="mb-6">
        <input
          type="range"
          min={0}
          max={duration}
          value={currentTime}
          onChange={handleSeek}
          className="range range-primary w-full"
          step="any"
        />
        <div className="flex justify-between text-sm text-base-content/60 mt-2">
          <span>{formatTime(currentTime)}</span>
          <span>{formatTime(duration)}</span>
        </div>
      </div>

      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {/* Play/Pause Button */}
          <button
            onClick={togglePlay}
            className="btn btn-circle btn-primary btn-lg shadow-lg hover:scale-105 transition-transform"
          >
            {isPlaying ? (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
              </svg>
            )}
          </button>

          {/* Volume Control */}
          <div className="flex items-center gap-3">
            <button
              onClick={() => {
                if (!audioRef.current) return;
                const newVolume = volume === 0 ? 1 : 0;
                audioRef.current.volume = newVolume;
                setVolume(newVolume);
              }}
              className="btn btn-ghost btn-circle"
            >
              {volume === 0 ? (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M14.657 2.929a1 1 0 011.414 0A9.972 9.972 0 0119 10a9.972 9.972 0 01-2.929 7.071 1 1 0 01-1.414-1.414A7.971 7.971 0 0017 10c0-2.21-.894-4.208-2.343-5.657a1 1 0 010-1.414zm-2.829 2.828a1 1 0 011.415 0A5.983 5.983 0 0115 10a5.984 5.984 0 01-1.757 4.243 1 1 0 01-1.415-1.415A3.984 3.984 0 0013 10a3.983 3.983 0 00-1.172-2.828 1 1 0 010-1.415z" clipRule="evenodd" />
                </svg>
              )}
            </button>
            <input
              type="range"
              min={0}
              max={1}
              value={volume}
              onChange={handleVolumeChange}
              step="any"
              className="range range-primary w-24"
            />
          </div>
        </div>

        {/* Version Selector and Speed Control */}
        <div className="flex items-center gap-3">
          {versions > 1 && (
            <select
              onChange={(e) => onVersionChange?.(Number(e.target.value))}
              className="select select-bordered select-sm"
              value={currentVersion}
            >
              {Array.from({ length: versions }, (_, i) => i + 1).map(v => (
                <option key={v} value={v}>Versi {v}</option>
              ))}
            </select>
          )}
          <select
            onChange={(e) => {
              if (!audioRef.current) return;
              audioRef.current.playbackRate = Number(e.target.value);
            }}
            className="select select-bordered select-sm"
            defaultValue="1"
          >
            <option value="0.5">0.5x</option>
            <option value="0.75">0.75x</option>
            <option value="1">1x</option>
            <option value="1.25">1.25x</option>
            <option value="1.5">1.5x</option>
            <option value="2">2x</option>
          </select>
        </div>
      </div>
    </div>
  );
};

export default function MusicDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [music, setMusic] = useState<Music | null>(null);
  const [loading, setLoading] = useState(true);
  const [isTriggeringCoverGeneration, setIsTriggerCoverGeneration] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [coverImage, setCoverImage] = useState<string | null>(null);
  const { setCurrentMusic, setIsPlaying, setCurrentVersion } = useMusicPlayer();
  const [activeVersion, setActiveVersion] = useState(1);
  const [pollingCover, setPollingCover] = useState(false);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const [downloadingVersion, setDownloadingVersion] = useState<number>(0);
  const [shareModalOpen, setShareModalOpen] = useState(false);
  const [showLyricsModal, setShowLyricsModal] = useState(false);
  const [isCopied, setIsCopied] = useState(false);

  useEffect(() => {
    const userStr = localStorage.getItem("user");
    if (userStr) {
      const user = JSON.parse(userStr);
      setCurrentUserId(user._id);
    }
  }, []);

  const handleCopyLyrics = async () => {
    const currentLyrics = music?.clips && music.clips.length > 0
      ? music.clips[activeVersion - 1]?.lyrics || ''
      : music?.lyrics || '';
    
    try {
      await navigator.clipboard.writeText(currentLyrics);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy lyrics:', err);
    }
  };

  const handleDownload = async (version: number = 1) => {
    if (!music) return;
    
    try {
      setDownloadingVersion(version);

      // Get audio URL from tracks first, then audioUrl
      let audioUrl = '';
      if (music.tracks && music.tracks.length > 0) {
        audioUrl = music.tracks[version - 1]?.audioUrl || '';
      } else {
        audioUrl = music.audioUrl || '';
      }

      if (!audioUrl) {
        alert('Tidak ada URL audio yang tersedia untuk diunduh.');
        return;
      }

      const fullUrl = audioUrl.startsWith('http') ? audioUrl : `${API_URL}${audioUrl}`;

      const xhr = new XMLHttpRequest();
      xhr.open('GET', fullUrl, true);
      xhr.responseType = 'blob';

      xhr.onload = function () {
        if (this.status === 200) {
          const blob = new Blob([this.response], { type: 'audio/mpeg' });
          const downloadUrl = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = downloadUrl;
          link.download = `${music.title} - Versi ${version}.mp3`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(downloadUrl);
        } else {
          alert(`Gagal mengunduh lagu (Status: ${this.status}). Silakan coba lagi nanti.`);
        }
      };

      xhr.onerror = function () {
        alert('Terjadi kesalahan jaringan saat mengunduh lagu. Silakan coba lagi nanti.');
      };

      xhr.send();
    } catch (error) {
      console.error('Error downloading file:', error);
      alert('Terjadi kesalahan saat mengunduh lagu. Silakan coba lagi nanti.');
    } finally {
      setDownloadingVersion(0);
    }
  };

  const startCoverPolling = async (musicId: string) => {
    const pollInterval = setInterval(async () => {
      try {
        const statusResponse = await musicService.getCoverStatus(musicId);
        if (
          statusResponse.data.status === "completed" &&
          statusResponse.data.result
        ) {
          setCoverImage(statusResponse.data.result);
          setPollingCover(false);
          clearInterval(pollInterval);
        } else if (statusResponse.data.status === "failed") {
          setPollingCover(false);
          clearInterval(pollInterval);
        }
      } catch (error) {
        console.error("Error polling cover status:", error);
        setPollingCover(false);
        clearInterval(pollInterval);
      }
    }, 5000);

    return () => clearInterval(pollInterval);
  };
  const fetchMusicDetail = async () => {
    if (!id) return;
    try {
      setLoading(true);
      const musicData = await musicService.getMusic(id);
      setMusic(musicData);
      setCoverImage(musicData.cover);

      try {
        const coverStatus = await musicService.getCoverStatus(id);
        if (coverStatus.data.status === "pending") {
          setPollingCover(true);
          startCoverPolling(id);
        } else if (
          coverStatus.data.status === "completed" &&
          coverStatus.data.result
        ) {
          setCoverImage(coverStatus.data.result);
        }
      } catch (error) {
        console.error("Error checking cover status:", error);
      }
    } catch (error) {
      console.error("Error fetching music details:", error);
      setError(
        "Failed to load music details. The music might have been removed or you don't have permission to view it."
      );
    } finally {
      setLoading(false);
    }
  };


  useEffect(() => {

    window.scrollTo(0, 0);
    fetchMusicDetail();
  }, [id]);

  useEffect(() => {
    if (music) {
      setCurrentMusic(music);
      setCurrentVersion(activeVersion);
      setIsPlaying(false);
    }
  }, [music, activeVersion, setCurrentMusic, setCurrentVersion, setIsPlaying]);


  const handleGenerateCover = async () => {
    if (id) {
      setIsTriggerCoverGeneration(true);
      try {
        await musicService.generateCover(id);
        setPollingCover(true);
        startCoverPolling(id);
        const modal = document.getElementById('success_generate_cover_modal');
        if (modal) (modal as HTMLDialogElement).showModal();
      } catch (error) {
        console.error("Error generating cover:", error);
        alert("Failed to generate cover. Please try again later.");
      } finally {
        setIsTriggerCoverGeneration(false);
      }
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[70vh]">
        <Loader />
      </div>
    );
  }


  if (error || !music) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[70vh] text-center text-white">
        <div className="bg-error/10 rounded-full p-4 mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-12 w-12 text-error"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
              clipRule="evenodd"
            />
          </svg>
        </div>
        <h2 className="text-2xl font-bold mb-2">Musik Tidak Ditemukan</h2>
        <p className="text-base-content/60 max-w-md mb-6">
          {error ||
            "Musik yang Anda cari tidak dapat ditemukan atau telah dihapus."}
        </p>
        <button onClick={() => navigate("/share")} className="btn btn-primary">
          Kembali ke Berbagi Musik
        </button>
      </div>
    );
  }

  // Get current track and audio URL
  const currentTrack = music?.tracks?.[activeVersion - 1];

  // Get audio URL from tracks first, then audioUrl
  const currentAudioUrl = currentTrack?.audioUrl || music?.audioUrl || '';

  // Get lyrics from music (tracks don't have individual lyrics)
  const currentLyrics = music?.lyrics || '';

  const isPlayable = ['SUCCESS', 'completed', 'succeeded'].includes(music?.status.toUpperCase() || '') && currentAudioUrl;

  return (
    <div className="min-h-screen bg-gradient-to-br from-base-200 to-base-300">
      {/* Modals */}
      <dialog id="success_generate_cover_modal" className="modal">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Cover Generation Started!</h3>
          <p className="pt-4 pb-2">Cover musik Anda sedang dibuat. Anda dapat menutup pesan ini dan menunggu hasilnya.</p>
          <div className="modal-action">
            <form method="dialog">
              <button className="btn" onClick={() => fetchMusicDetail()}>Close</button>
            </form>
          </div>
        </div>
      </dialog>

      <dialog id="my_modal_2" className="modal">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Confirmation</h3>
          <p className="py-4">Are you sure you want to regenerate the cover image?</p>
          <div className="modal-action">
            <form method="dialog">
              <button className="btn text-primary mr-2">Cancel</button>
              <button className="btn btn-primary text-accent" onClick={() => handleGenerateCover()}>Confirm</button>
            </form>
          </div>
        </div>
      </dialog>

      {/* Header with Back Button */}
      <div className="sticky top-0 z-30 bg-base-100/80 backdrop-blur-md border-b border-base-200">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <button
              onClick={() => navigate(-1)}
              className="btn btn-ghost btn-circle"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <div className="flex items-center gap-2">
              <button
                onClick={() => setShareModalOpen(true)}
                className="btn btn-ghost btn-circle"
                title="Bagikan"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="18" cy="5" r="3"></circle>
                  <circle cx="6" cy="12" r="3"></circle>
                  <circle cx="18" cy="19" r="3"></circle>
                  <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
                  <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
                </svg>
              </button>
              {currentUserId && music?.userId?._id === currentUserId && (
                <button
                  title="RE-Generate music cover"
                  className="btn btn-ghost btn-circle"
                  onClick={() => {
                    const modal = document.getElementById('my_modal_2');
                    if (modal) (modal as HTMLDialogElement).showModal();
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="h-5 w-5">
                    <path fillRule="evenodd" d="M4.755 10.059a7.5 7.5 0 0 1 12.548-3.364l1.903 1.903h-3.183a.75.75 0 1 0 0 1.5h4.992a.75.75 0 0 0 .75-.75V4.356a.75.75 0 0 0-1.5 0v3.18l-1.9-1.9A9 9 0 0 0 3.306 9.67a.75.75 0 1 0 1.45.388Zm15.408 3.352a.75.75 0 0 0-.919.53 7.5 7.5 0 0 1-12.548 3.364l-1.902-1.903h3.183a.75.75 0 0 0 0-1.5H2.984a.75.75 0 0 0-.75.75v4.992a.75.75 0 0 0 1.5 0v-3.18l1.9 1.9a9 9 0 0 0 15.059-*********** 0 0 0-.53-.918Z" clipRule="evenodd" />
                  </svg>
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6 max-w-4xl">
        {/* Hero Section */}
        <div className="bg-base-100 rounded-3xl shadow-xl overflow-hidden mb-6">
          <div className="relative">
            {/* Cover Image */}
            <div className="relative h-80 md:h-96">
              <img
                src={coverImage || defaultCoverImage}
                alt={music?.title}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
              
              {/* Cover Generation Loading */}
              {(isTriggeringCoverGeneration || pollingCover) && (
                <div className="absolute inset-0 flex items-center justify-center bg-black/70 backdrop-blur-sm">
                  <div className="text-center text-white">
                    <Loader />
                    <p className="mt-4">
                      {isTriggeringCoverGeneration ? 'Triggering cover generation...' : 'Generating cover image...'}
                    </p>
                  </div>
                </div>
              )}

              {/* Music Info Overlay */}
              <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                <div className="mb-3">
                  {music?.tags && (
                    <div className="flex flex-wrap gap-2 mb-3">
                      {music.tags.split(',').map((tag, index) => (
                        <span key={index} className="badge badge-primary badge-sm">
                          {tag.trim()}
                        </span>
                      ))}
                    </div>
                  )}
                  <h1 className="text-3xl md:text-4xl font-bold mb-2">{music?.title}</h1>
                  <div className="flex items-center gap-3">
                    <div className="avatar">
                      <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clipRule="evenodd" />
                        </svg>
                      </div>
                    </div>
                    <div>
                      <p className="text-sm font-medium">{music?.userId?.name || 'Unknown'}</p>
                      <p className="text-xs text-white/70">
                        {music?.userId?.author_total_music_count || 0} Lagu • {
                          currentClip?.duration 
                            ? `${Math.floor(currentClip.duration / 60)}:${String(Math.floor(currentClip.duration % 60)).padStart(2, '0')}`
                            : music?.metadata?.duration 
                              ? `${Math.floor(music.metadata.duration / 60)}:${String(Math.floor(music.metadata.duration % 60)).padStart(2, '0')}`
                              : '0:00'
                        }
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Version Selector */}
        {(music?.tracks && music.tracks.length > 1) && (
          <div className="bg-base-100 rounded-2xl shadow-lg p-4 mb-6">
            <h3 className="text-lg font-semibold mb-3">Pilih Versi</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
              {(music.tracks || []).map((item, index) => {
                const duration = 'duration' in item ? item.duration : 0;
                const itemId = 'id' in item ? item.id : index;
                return (
                  <button
                    key={itemId}
                    onClick={() => setActiveVersion(index + 1)}
                    className={`p-4 rounded-xl border-2 transition-all duration-200 ${
                      activeVersion === index + 1
                        ? 'border-primary bg-primary/10 text-primary'
                        : 'border-base-300 hover:border-primary/50 hover:bg-base-200'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${
                        activeVersion === index + 1 ? 'bg-primary/20' : 'bg-base-300'
                      }`}>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
                          <polygon points="5 3 19 12 5 21 5 3"></polygon>
                        </svg>
                      </div>
                      <div className="text-left">
                        <p className="font-medium">Versi {index + 1}</p>
                        <p className="text-xs opacity-70">
                          {Math.floor((duration || 0) / 60)}:{String(Math.floor((duration || 0) % 60)).padStart(2, '0')}
                        </p>
                      </div>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        )}

        {/* Music Player */}
        {isPlayable && (
          <div className="mb-6">
            <MusicPlayer
              audioUrl={currentAudioUrl}
              onVersionChange={setActiveVersion}
              versions={music?.tracks?.length || 1}
              currentVersion={activeVersion}
            />
          </div>
        )}

        {/* Action Buttons */}
        <div className="bg-base-100 rounded-2xl shadow-lg p-6 mb-6">
          <h3 className="text-lg font-semibold mb-4">Aksi</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Download Buttons */}
            {isPlayable && (music?.tracks && music.tracks.length > 1) ? (
              (music.tracks || []).map((_, index) => (
                <button
                  key={index}
                  onClick={() => handleDownload(index + 1)}
                  disabled={downloadingVersion === index + 1}
                  className="btn btn-outline gap-2 hover:btn-primary transition-all duration-200"
                >
                  {downloadingVersion === index + 1 ? (
                    <div className="loading loading-spinner loading-sm"></div>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4" />
                      <polyline points="7 10 12 15 17 10" />
                      <line x1="12" y1="15" x2="12" y2="3" />
                    </svg>
                  )}
                  Unduh Versi {index + 1}
                </button>
              ))
            ) : isPlayable ? (
              <button
                onClick={() => handleDownload(1)}
                disabled={downloadingVersion === 1}
                className="btn btn-outline gap-2 hover:btn-primary transition-all duration-200"
              >
                {downloadingVersion === 1 ? (
                  <div className="loading loading-spinner loading-sm"></div>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4" />
                    <polyline points="7 10 12 15 17 10" />
                    <line x1="12" y1="15" x2="12" y2="3" />
                  </svg>
                )}
                Unduh Lagu
              </button>
            ) : null}

            {/* View Lyrics Button */}
            {currentLyrics && (
              <button
                onClick={() => setShowLyricsModal(true)}
                className="btn btn-outline gap-2 hover:btn-secondary transition-all duration-200"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <polyline points="14,2 14,8 20,8"></polyline>
                  <line x1="16" y1="13" x2="8" y2="13"></line>
                  <line x1="16" y1="17" x2="8" y2="17"></line>
                  <polyline points="10,9 9,9 8,9"></polyline>
                </svg>
                Lihat Lirik
              </button>
            )}

            {/* Share Button */}
            <button
              onClick={() => setShareModalOpen(true)}
              className="btn btn-outline gap-2 hover:btn-accent transition-all duration-200"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="18" cy="5" r="3"></circle>
                <circle cx="6" cy="12" r="3"></circle>
                <circle cx="18" cy="19" r="3"></circle>
                <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
                <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
              </svg>
              Bagikan
            </button>
          </div>
        </div>

        {/* Music Info */}
        <div className="bg-base-100 rounded-2xl shadow-lg p-6">
          <h3 className="text-lg font-semibold mb-4">Informasi Musik</h3>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-base-content/70">Prompt</label>
              <p className="text-base-content mt-1">{music?.prompt || 'Tidak ada prompt'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-base-content/70">Status</label>
              <div className="mt-1">
                <span className={`badge ${
                  ['SUCCESS', 'completed', 'succeeded'].includes(music?.status.toUpperCase() || '') 
                    ? 'badge-success' 
                    : ['PENDING', 'TEXT_SUCCESS', 'FIRST_SUCCESS', 'pending'].includes(music?.status.toUpperCase() || '')
                      ? 'badge-warning'
                      : 'badge-error'
                }`}>
                  {music?.status === 'SUCCESS' ? 'Selesai' : music?.status || 'Unknown'}
                </span>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-base-content/70">Dibuat</label>
              <p className="text-base-content mt-1">
                {music?.createdAt ? new Date(music.createdAt).toLocaleDateString('id-ID', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                }) : 'Tidak diketahui'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Lyrics Modal */}
      {showLyricsModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <div
            className="fixed inset-0 bg-black/60 backdrop-blur-sm transition-opacity"
            onClick={() => setShowLyricsModal(false)}
          />
          <div className="relative bg-base-100 rounded-2xl max-w-2xl w-full mx-4 shadow-2xl border border-base-200 max-h-[80vh] overflow-hidden">
            {/* Header */}
            <div className="flex items-center justify-between px-6 py-4 border-b border-base-200">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14,2 14,8 20,8"></polyline>
                    <line x1="16" y1="13" x2="8" y2="13"></line>
                    <line x1="16" y1="17" x2="8" y2="17"></line>
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold">{music?.title}</h3>
                  <p className="text-xs text-base-content/60">
                    Lirik {music?.clips && music.clips.length > 1 ? `Versi ${activeVersion}` : ''}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={handleCopyLyrics}
                  className="btn btn-ghost btn-sm gap-2"
                >
                  {isCopied ? (
                    <>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-success" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span className="text-success">Tersalin!</span>
                    </>
                  ) : (
                    <>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M7 9a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H9a2 2 0 01-2-2V9z" />
                        <path d="M5 3a2 2 0 00-2 2v6a2 2 0 002 2V5h8a2 2 0 00-2-2H5z" />
                      </svg>
                      <span>Salin</span>
                    </>
                  )}
                </button>
                <button
                  onClick={() => setShowLyricsModal(false)}
                  className="btn btn-ghost btn-sm btn-square"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6 overflow-y-auto" style={{ maxHeight: 'calc(80vh - 120px)' }}>
              <div className="bg-base-200/50 rounded-xl p-6">
                <p className="whitespace-pre-wrap text-base-content/90 leading-relaxed">
                  {currentLyrics}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Share Modal */}
      {shareModalOpen && music && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <div
            className="fixed inset-0 bg-black/60 backdrop-blur-sm transition-opacity"
            onClick={() => setShareModalOpen(false)}
          />
          <div className="relative z-50">
            <ShareMusicModal musicId={music._id} />
          </div>
        </div>
      )}
    </div>
  );
}
