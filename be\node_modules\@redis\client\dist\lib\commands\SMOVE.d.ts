import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: false;
    /**
     * Constructs the SMOVE command
     *
     * @param parser - The command parser
     * @param source - The source set key
     * @param destination - The destination set key
     * @param member - The member to move
     * @see https://redis.io/commands/smove/
     */
    readonly parseCommand: (this: void, parser: CommandParser, source: RedisArgument, destination: RedisArgument, member: RedisArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=SMOVE.d.ts.map