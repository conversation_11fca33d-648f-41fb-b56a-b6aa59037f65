import { CommandParser } from '../client/parser';
import { RedisArgument, BlobStringReply, NullReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the SRANDMEMBER command to get a random member from a set
     *
     * @param parser - The command parser
     * @param key - The key of the set to get random member from
     * @see https://redis.io/commands/srandmember/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument) => void;
    readonly transformReply: () => BlobStringReply | NullReply;
};
export default _default;
//# sourceMappingURL=SRANDMEMBER.d.ts.map