import { Command<PERSON>arser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
/**
 * Command for removing a consumer group
 */
declare const _default: {
    readonly IS_READ_ONLY: false;
    /**
     * Constructs the XGROUP DESTROY command to remove a consumer group
     *
     * @param parser - The command parser
     * @param key - The stream key
     * @param group - Name of the consumer group to destroy
     * @returns 1 if the group was destroyed, 0 if it did not exist
     * @see https://redis.io/commands/xgroup-destroy/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, group: RedisArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=XGROUP_DESTROY.d.ts.map