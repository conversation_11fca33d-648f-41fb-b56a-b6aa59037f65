"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var UploadProcessor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploadProcessor = void 0;
const bull_1 = require("@nestjs/bull");
const common_1 = require("@nestjs/common");
const constant_1 = require("../constant");
const storage_service_1 = require("../../storage/storage.service");
const music_service_1 = require("../../music/music.service");
let UploadProcessor = UploadProcessor_1 = class UploadProcessor {
    constructor(storageService, musicService) {
        this.storageService = storageService;
        this.musicService = musicService;
        this.logger = new common_1.Logger(UploadProcessor_1.name);
        this.logger.log('UploadProcessor initialized');
    }
    async handleUploadAudio(job) {
        try {
            const { musicId, fileId, fileUrl } = job.data;
            await job.log(`Uploading audio from URL: ${fileUrl}`);
            const audioUrl = await this.storageService.uploadFromUrl(fileUrl, `music/${musicId}/${fileId}.mp3`);
            await job.log(`Audio uploaded successfully: ${audioUrl}`);
            await this.musicService.updateAudioUrl(musicId, fileId, audioUrl);
            return {
                musicId,
                fileId,
                fileUrl: audioUrl
            };
        }
        catch (error) {
            throw new Error(error);
        }
    }
    async handleUploadCover(job) {
        try {
            const { musicId, fileId, fileUrl } = job.data;
            await job.log(`Uploading cover from URL: ${fileUrl}`);
            const coverUrl = await this.storageService.uploadFromUrl(fileUrl, `music/${musicId}/${fileId}.png`);
            await job.log(`Cover uploaded successfully: ${coverUrl}`);
            await this.musicService.updateCoverUrl(musicId, fileId, coverUrl);
            return {
                musicId,
                fileUrl: coverUrl
            };
        }
        catch (error) {
            throw new Error(error);
        }
    }
    onActive(job) {
        this.logger.log(`Processing job ${job.id} of type ${job.name}`);
    }
    onCompleted(job, result) {
        this.logger.log(`Job ${job.id} completed with result: ${JSON.stringify(result)}`);
    }
    onFailed(job, err) {
        this.logger.error(`Job ${job.id} failed with error: ${err.message}`, err.stack);
    }
    onError(err) {
        this.logger.error(`Queue error: ${err.message}`, err.stack);
    }
};
exports.UploadProcessor = UploadProcessor;
__decorate([
    (0, bull_1.Process)(constant_1.QUEUE_UPLOAD_AUDIO_JOB),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UploadProcessor.prototype, "handleUploadAudio", null);
__decorate([
    (0, bull_1.Process)(constant_1.QUEUE_UPLOAD_COVER_JOB),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UploadProcessor.prototype, "handleUploadCover", null);
__decorate([
    (0, bull_1.OnQueueActive)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], UploadProcessor.prototype, "onActive", null);
__decorate([
    (0, bull_1.OnQueueCompleted)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], UploadProcessor.prototype, "onCompleted", null);
__decorate([
    (0, bull_1.OnQueueFailed)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Error]),
    __metadata("design:returntype", void 0)
], UploadProcessor.prototype, "onFailed", null);
__decorate([
    (0, bull_1.OnQueueError)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Error]),
    __metadata("design:returntype", void 0)
], UploadProcessor.prototype, "onError", null);
exports.UploadProcessor = UploadProcessor = UploadProcessor_1 = __decorate([
    (0, bull_1.Processor)(constant_1.QUEUE_UPLOAD_FILE),
    __metadata("design:paramtypes", [storage_service_1.StorageService,
        music_service_1.MusicService])
], UploadProcessor);
//# sourceMappingURL=upload.processor.js.map