declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Read-only variant of SORT that sorts the elements in a list, set or sorted set.
     * @param args - Same parameters as the SORT command.
     */
    readonly parseCommand: (this: void, parser: import("../..").CommandParser, key: import("../RESP/types").RedisArgument, options?: import("./SORT").SortOptions | undefined) => void;
    readonly transformReply: () => import("../RESP/types").ArrayReply<import("../RESP/types").BlobStringReply<string>>;
};
export default _default;
//# sourceMappingURL=SORT_RO.d.ts.map