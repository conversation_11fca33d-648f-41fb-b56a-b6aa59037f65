import { OnModuleInit } from '@nestjs/common';
import { Queue } from 'bull';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { GenerateMusicQueueDto } from './dto/generate-music-queue.dto';
import { GenerateCoverQueueDto } from './dto/generate-cover-queue.dto';
import { UploadFileQueueDto } from './dto/upload-file-queue.dto';
export declare class QueueService implements OnModuleInit {
    private musicQueue;
    private coverQueue;
    private fileQueue;
    private readonly eventEmitter;
    private readonly logger;
    constructor(musicQueue: Queue, coverQueue: Queue, fileQueue: Queue, eventEmitter: EventEmitter2);
    onModuleInit(): void;
    generateMusic(data: GenerateMusicQueueDto): Promise<import("bull").Job<any>>;
    generateCover(data: GenerateCoverQueueDto): Promise<import("bull").Job<any>>;
    uploadAudio(data: UploadFileQueueDto): Promise<import("bull").Job<any>>;
    uploadCover(data: UploadFileQueueDto): Promise<import("bull").Job<any>>;
}
