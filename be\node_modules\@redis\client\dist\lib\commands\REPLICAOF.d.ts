import { CommandParser } from '../client/parser';
import { SimpleStringReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the REPLICAO<PERSON> command
     *
     * @param parser - The command parser
     * @param host - The host of the master to replicate from
     * @param port - The port of the master to replicate from
     * @see https://redis.io/commands/replicaof/
     */
    readonly parseCommand: (this: void, parser: CommandParser, host: string, port: number) => void;
    readonly transformReply: () => SimpleStringReply;
};
export default _default;
//# sourceMappingURL=REPLICAOF.d.ts.map