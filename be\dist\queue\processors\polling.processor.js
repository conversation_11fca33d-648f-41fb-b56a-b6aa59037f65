"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PollingProcessor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PollingProcessor = void 0;
const bull_1 = require("@nestjs/bull");
const common_1 = require("@nestjs/common");
const constant_1 = require("../constant");
const piapi_service_1 = require("../../libs/piapi/piapi.service");
const storage_service_1 = require("../../storage/storage.service");
const polling_service_1 = require("../polling.service");
const music_service_1 = require("../../music/music.service");
let PollingProcessor = PollingProcessor_1 = class PollingProcessor {
    constructor(pollingService, musicService, piapiService, storageService) {
        this.pollingService = pollingService;
        this.musicService = musicService;
        this.piapiService = piapiService;
        this.storageService = storageService;
        this.logger = new common_1.Logger(PollingProcessor_1.name);
        this.logger.log('PollingProcessor initialized');
    }
    async handleMusicStatus(job) {
        try {
            await job.log('Polling music status...');
            const result = await this.musicService.getMusicStatus(job.data.taskId);
            if (result.status === 'SUCCESS') {
                await job.log('Music status: SUCCESS');
                await this.pollingService.stopPollingMusicStatus(job.data.taskId);
                await job.log('Music status polling stopped');
            }
            else {
                await job.log(`Music ${job.data.musicId} status: ${result.status}, continue polling...`);
                this.logger.log(`Music ${job.data.musicId} status: ${result.status}`);
            }
            return {
                status: result.status,
                musicId: job.data.musicId
            };
        }
        catch (err) {
            this.logger.error(`Polling job ${job.id} failed: ${err.message}`, err.stack);
            await this.pollingService.stopPollingMusicStatus(job.data.taskId);
            throw new Error(err.message);
        }
    }
    async handleCoverStatus(job) {
        try {
            await job.log('Polling music cover status...');
            const result = await this.musicService.getCoverStatus(job.data.taskId);
            if (result.success && result.images) {
                await job.log('Music covers generated successfully');
                await this.pollingService.stopPollingCoverStatus(job.data.taskId);
                await job.log('Music cover status polling stopped');
            }
            else {
                await job.log(`Music cover generation still in progress, continue polling...`);
            }
            return result;
        }
        catch (err) {
            this.logger.error(`Polling job ${job.id} failed: ${err.message}`, err.stack);
            throw err;
        }
    }
    onActive(job) {
        this.logger.log(`Processing polling job ${job.id} of type ${job.name}`);
    }
    onCompleted(job, result) {
        this.logger.log(`Polling job ${job.id} completed`);
    }
    onFailed(job, err) {
        this.logger.error(`Polling job ${job.id} failed with error: ${err.message}`, err.stack);
    }
};
exports.PollingProcessor = PollingProcessor;
__decorate([
    (0, bull_1.Process)(constant_1.QUEUE_POLLING_MUSIC_STATUS_JOB),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PollingProcessor.prototype, "handleMusicStatus", null);
__decorate([
    (0, bull_1.Process)(constant_1.QUEUE_POLLING_COVER_STATUS_JOB),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PollingProcessor.prototype, "handleCoverStatus", null);
__decorate([
    (0, bull_1.OnQueueActive)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], PollingProcessor.prototype, "onActive", null);
__decorate([
    (0, bull_1.OnQueueCompleted)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], PollingProcessor.prototype, "onCompleted", null);
__decorate([
    (0, bull_1.OnQueueFailed)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Error]),
    __metadata("design:returntype", void 0)
], PollingProcessor.prototype, "onFailed", null);
exports.PollingProcessor = PollingProcessor = PollingProcessor_1 = __decorate([
    (0, bull_1.Processor)(constant_1.QUEUE_POLLING),
    __metadata("design:paramtypes", [polling_service_1.PollingService,
        music_service_1.MusicService,
        piapi_service_1.PiapiService,
        storage_service_1.StorageService])
], PollingProcessor);
//# sourceMappingURL=polling.processor.js.map