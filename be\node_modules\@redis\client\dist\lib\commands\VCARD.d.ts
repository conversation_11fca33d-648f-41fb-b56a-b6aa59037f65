import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Retrieve the number of elements in a vector set
     *
     * @param parser - The command parser
     * @param key - The key of the vector set
     * @see https://redis.io/commands/vcard/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=VCARD.d.ts.map