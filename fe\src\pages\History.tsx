import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { musicService } from '../services/api';
import { useMusicPlayer } from '../contexts/MusicPlayerContext';
import { API_URL } from '../config/api';

import Loader from '../components/Loader';
import defaultCoverImage from '../assets/wood-blog-placeholder.jpg';
import { useNavigate } from 'react-router-dom';
import { ShareMusicModal } from '../components/ShareMusicModal';
import GeneratingMusicCard from '../components/GeneratingMusicCard';

import { Music } from "../types";

interface MusicPlayerProps {
  audioUrl: string;
  onVersionChange?: (version: number) => void;
  versions?: number;
  currentVersion?: number;
}

const MusicPlayer = ({ audioUrl, onVersionChange, versions = 1, currentVersion = 1 }: MusicPlayerProps) => {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleLoadedMetadata = () => {
      setDuration(audio.duration);
    };

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setCurrentTime(0);
    };

    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('ended', handleEnded);

    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('ended', handleEnded);
    };
  }, []);

  const togglePlay = () => {
    if (!audioRef.current) return;

    if (isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!audioRef.current) return;
    const time = Number(e.target.value);
    audioRef.current.currentTime = time;
    setCurrentTime(time);
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!audioRef.current) return;
    const vol = Number(e.target.value);
    audioRef.current.volume = vol;
    setVolume(vol);
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="bg-base-200 rounded-xl p-4">
      <audio ref={audioRef} src={audioUrl} preload="metadata" />

      {/* Progress Bar */}
      <div className="mb-4">
        <input
          type="range"
          min={0}
          max={duration}
          value={currentTime}
          onChange={handleSeek}
          className="range range-xs range-primary w-full"
          step="any"
        />
        <div className="flex justify-between text-xs text-base-content/60 mt-1">
          <span>{formatTime(currentTime)}</span>
          <span>{formatTime(duration)}</span>
        </div>
      </div>

      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {/* Play/Pause Button */}
          <button
            onClick={togglePlay}
            className="btn btn-circle btn-primary btn-sm"
          >
            {isPlaying ? (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
              </svg>
            )}
          </button>

          {/* Volume Control */}
          <div className="flex items-center gap-2">
            <button
              onClick={() => {
                if (!audioRef.current) return;
                const newVolume = volume === 0 ? 1 : 0;
                audioRef.current.volume = newVolume;
                setVolume(newVolume);
              }}
              className="btn btn-ghost btn-circle btn-sm"
            >
              {volume === 0 ? (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M14.657 2.929a1 1 0 011.414 0A9.972 9.972 0 0119 10a9.972 9.972 0 01-2.929 7.071 1 1 0 01-1.414-1.414A7.971 7.971 0 0117 10c0-2.21-.894-4.208-2.343-5.657a1 1 0 010-1.414zm-2.829 2.828a1 1 0 011.415 0A5.983 5.983 0 0115 10a5.984 5.984 0 01-1.757 4.243 1 1 0 01-1.415-1.415A3.984 3.984 0 0013 10a3.983 3.983 0 00-1.172-2.828 1 1 0 010-1.415z" clipRule="evenodd" />
                </svg>
              )}
            </button>
            <input
              type="range"
              min={0}
              max={1}
              value={volume}
              onChange={handleVolumeChange}
              step="any"
              className="range range-xs range-primary w-20"
            />
          </div>
        </div>

        {/* Playback Speed */}
        <div className="flex items-center gap-2">
          {versions > 1 && (
            <select
              onChange={(e) => onVersionChange?.(Number(e.target.value))}
              className="select select-bordered select-xs"
              value={currentVersion}
            >
              {Array.from({ length: versions }, (_, i) => i + 1).map(v => (
                <option key={v} value={v}>Versi {v}</option>
              ))}
            </select>
          )}
          <select
            onChange={(e) => {
              if (!audioRef.current) return;
              audioRef.current.playbackRate = Number(e.target.value);
            }}
            className="select select-bordered select-xs"
            defaultValue="1"
          >
            <option value="0.5">0.5x</option>
            <option value="0.75">0.75x</option>
            <option value="1">1x</option>
            <option value="1.25">1.25x</option>
            <option value="1.5">1.5x</option>
            <option value="2">2x</option>
          </select>
        </div>
      </div>
    </div>
  );
};

export default function History() {
  const { setCurrentMusic, setIsPlaying, setCurrentVersion } = useMusicPlayer();
  const [musics, setMusics] = useState<Music[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedMusic, setSelectedMusic] = useState<Music | null>(null);
  const [showLyricsModal, setShowLyricsModal] = useState(false);
  const [selectedLyrics, setSelectedLyrics] = useState<string>('');
  const [downloadingId, setDownloadingId] = useState<string>('');
  const [downloadingVersion, setDownloadingVersion] = useState<number>(0);
  const [selectedTitle, setSelectedTitle] = useState<string>('');
  const [pollingIds, setPollingIds] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isCopied, setIsCopied] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'title' | 'status'>('newest');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [downloadModalOpen, setDownloadModalOpen] = useState(false);
  const [selectedMusicForDownload, setSelectedMusicForDownload] = useState<Music | null>(null);
  const navigate = useNavigate();

  const [generatingCovers, setGeneratingCovers] = useState<Set<string>>(new Set());
  const [coverImages, setCoverImages] = useState<Record<string, string>>({});
  const [forceUpdate, setForceUpdate] = useState(false);

  const handleCopyLyrics = async () => {
    try {
      await navigator.clipboard.writeText(selectedLyrics);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy lyrics:', err);
    }
  };

  type MusicStatus = 'SUCCESS' | 'FAILED' | 'PENDING' | 'GENERATING_LYRICS' | 'LYRICS_GENERATED' | 'GENERATING_MUSIC' | 'MUSIC_GENERATED' | 'FIRST_TRACK_GENERATED' | 'GENERATING_COVER' | 'COVER_GENERATED' | 'TEXT_SUCCESS' | 'FIRST_SUCCESS' | 'GENERATE_AUDIO_FAILED' | 'CREATE_TASK_FAILED' | 'completed' | 'failed' | 'pending';

  const getStatusColor = (status: string) => {
    switch (status.toUpperCase()) {
      case 'SUCCESS':
      case 'COMPLETED':
      case 'SUCCEEDED':
        return 'badge-success';
      case 'PENDING':
      case 'GENERATING_LYRICS':
      case 'LYRICS_GENERATED':
      case 'GENERATING_MUSIC':
      case 'MUSIC_GENERATED':
      case 'FIRST_TRACK_GENERATED':
      case 'GENERATING_COVER':
      case 'COVER_GENERATED':
      case 'TEXT_SUCCESS':
      case 'FIRST_SUCCESS':
        return 'badge-warning';
      case 'FAILED':
      case 'GENERATE_AUDIO_FAILED':
      case 'CREATE_TASK_FAILED':
        return 'badge-error';
      default:
        return 'badge-ghost';
    }
  };

  const getStatusDisplay = (status: string) => {
    switch (status.toUpperCase()) {
      case 'PENDING':
        return (
          <div className="flex items-center gap-2">
            <span>Menunggu</span>
            <Loader size="small" />
          </div>
        );
      case 'GENERATING_LYRICS':
        return (
          <div className="flex items-center gap-2">
            <span>Membuat Lirik</span>
            <Loader size="small" />
          </div>
        );
      case 'LYRICS_GENERATED':
        return (
          <div className="flex items-center gap-2">
            <span>Lirik Selesai</span>
            <Loader size="small" />
          </div>
        );
      case 'GENERATING_MUSIC':
        return (
          <div className="flex items-center gap-2">
            <span>Membuat Audio</span>
            <Loader size="small" />
          </div>
        );
      case 'MUSIC_GENERATED':
        return (
          <div className="flex items-center gap-2">
            <span>Audio Selesai</span>
            <Loader size="small" />
          </div>
        );
      case 'FIRST_TRACK_GENERATED':
        return (
          <div className="flex items-center gap-2">
            <span>Track Pertama Selesai</span>
            <Loader size="small" />
          </div>
        );
      case 'GENERATING_COVER':
        return (
          <div className="flex items-center gap-2">
            <span>Membuat Cover</span>
            <Loader size="small" />
          </div>
        );
      case 'COVER_GENERATED':
        return (
          <div className="flex items-center gap-2">
            <span>Cover Selesai</span>
            <Loader size="small" />
          </div>
        );
      case 'TEXT_SUCCESS':
        return (
          <div className="flex items-center gap-2">
            <span>Membuat Lirik</span>
            <Loader size="small" />
          </div>
        );
      case 'FIRST_SUCCESS':
        return (
          <div className="flex items-center gap-2">
            <span>Membuat Audio</span>
            <Loader size="small" />
          </div>
        );
      case 'SUCCESS':
      case 'COMPLETED':
      case 'SUCCEEDED':
        return 'Selesai';
      case 'FAILED':
      case 'GENERATE_AUDIO_FAILED':
      case 'CREATE_TASK_FAILED':
        return 'Gagal';
      default:
        return status;
    }
  };

  // Filter and sort musics based on search query, status, and sort option
  const filteredAndSortedMusics = useMemo(() => {
    let filtered = musics.filter((music: Music) => {
      const matchesSearch = music.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        music.prompt.toLowerCase().includes(searchQuery.toLowerCase()) ||
        music.tags?.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesStatus = statusFilter === 'all' ||
        (statusFilter === 'completed' && ['SUCCESS', 'completed', 'succeeded'].includes(music.status.toUpperCase())) ||
        (statusFilter === 'pending' && ['PENDING', 'TEXT_SUCCESS', 'FIRST_SUCCESS', 'pending'].includes(music.status.toUpperCase())) ||
        (statusFilter === 'failed' && ['FAILED', 'GENERATE_AUDIO_FAILED', 'CREATE_TASK_FAILED', 'failed'].includes(music.status.toUpperCase()));
      return matchesSearch && matchesStatus;
    });

    // Sort the filtered results
    return filtered.sort((a: Music, b: Music) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'oldest':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        case 'title':
          return a.title.localeCompare(b.title);
        case 'status':
          return a.status.localeCompare(b.status);
        default:
          return 0;
      }
    });
  }, [musics, searchQuery, statusFilter, sortBy]);

  // Calculate statistics
  const stats = {
    total: musics.length,
    completed: musics.filter(m => ['SUCCESS', 'completed', 'succeeded'].includes(m.status.toUpperCase())).length,
    pending: musics.filter(m => ['PENDING', 'GENERATING_LYRICS', 'LYRICS_GENERATED', 'GENERATING_MUSIC', 'MUSIC_GENERATED', 'FIRST_TRACK_GENERATED', 'GENERATING_COVER', 'COVER_GENERATED', 'TEXT_SUCCESS', 'FIRST_SUCCESS', 'pending'].includes(m.status.toUpperCase())).length,
    failed: musics.filter(m => ['FAILED', 'GENERATE_AUDIO_FAILED', 'CREATE_TASK_FAILED', 'failed'].includes(m.status.toUpperCase())).length
  };

  const loadMusics = async () => {
    try {
      const data = await musicService.listMusic();
      setMusics(data.data.userMusics);

      // Initialize cover images from existing covers
      const existingCovers: Record<string, string> = {};
      data.data.userMusics.forEach((music: Music) => {
        if (music.cover) {
          existingCovers[music._id] = music.cover;
        }
      });
      setCoverImages(existingCovers);

      // Start polling for pending musics
      const pendingMusicIds: string[] = data.data.userMusics
        .filter((music: Music) => ['PENDING', 'GENERATING_LYRICS', 'LYRICS_GENERATED', 'GENERATING_MUSIC', 'MUSIC_GENERATED', 'FIRST_TRACK_GENERATED', 'GENERATING_COVER', 'COVER_GENERATED', 'TEXT_SUCCESS', 'FIRST_SUCCESS', 'pending'].includes(music.status.toUpperCase()))
        .map((music: Music) => music._id);

      setPollingIds(new Set(pendingMusicIds));
    } catch (error) {
      console.error('Error loading music:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateCover = async (musicId: string) => {
    try {
      // Mark as generating
      setGeneratingCovers(prev => {
        const updated = new Set(prev);
        updated.add(musicId);
        return updated;
      });

      console.log(`Starting cover generation for music ${musicId}`);

      // Start cover generation
      const response = await musicService.generateCover(musicId);
      console.log(`Cover generation initiated for music ${musicId}:`, response);

      // Start polling immediately
      startPollingCover(musicId);

      // Also set a backup timer to check status after 10 seconds
      setTimeout(() => {
        checkCoverDirectly(musicId);
      }, 10000);

    } catch (error) {
      console.error(`Error generating cover for music ${musicId}:`, error);

      // Remove from generating set
      setGeneratingCovers(prev => {
        const updated = new Set(prev);
        updated.delete(musicId);
        return updated;
      });

      alert("Failed to generate cover. Please try again later.");
    }
  };

  // New function to start polling
  const startPollingCover = (musicId: string) => {
    console.log(`Starting polling for music ${musicId}`);

    // Create a polling interval
    const intervalId = setInterval(async () => {
      try {
        const statusResponse = await musicService.getCoverStatus(musicId);
        console.log(`Polling cover status for ${musicId}:`, statusResponse);

        if (statusResponse.data.status === "completed" && statusResponse.data.result) {
          console.log(`Cover generation completed for ${musicId}:`, statusResponse.data.result);

          // Add timestamp to break cache
          const coverUrl = `${statusResponse.data.result}?t=${new Date().getTime()}`;

          // Update coverImages state
          setCoverImages(prev => ({
            ...prev,
            [musicId]: coverUrl
          }));

          // Update music list
          setMusics(prev =>
            prev.map(music =>
              music._id === musicId
                ? { ...music, cover: coverUrl, coverGenerating: false }
                : music
            )
          );

          // Remove from generating set
          setGeneratingCovers(prev => {
            const updated = new Set(prev);
            updated.delete(musicId);
            return updated;
          });

          // Force re-render
          setForceUpdate(prev => !prev);

          // Clear interval
          clearInterval(intervalId);
        }
        else if (statusResponse.data.status === "failed") {
          console.log(`Cover generation failed for ${musicId}`);

          // Remove from generating set
          setGeneratingCovers(prev => {
            const updated = new Set(prev);
            updated.delete(musicId);
            return updated;
          });

          // Clear interval
          clearInterval(intervalId);
        }
      } catch (error) {
        console.error(`Error polling cover status for ${musicId}:`, error);
      }
    }, 3000); // Poll every 3 seconds

    // Store interval ID to clear it later if needed
    return intervalId;
  };

  // New function to check cover directly
  const checkCoverDirectly = async (musicId: string) => {
    try {
      console.log(`Direct check for cover status of ${musicId}`);

      // Get the latest music data directly
      const refreshedMusic = await musicService.getMusic(musicId);

      // Check if cover exists in the refreshed data
      if (refreshedMusic.cover) {
        console.log(`Cover found in refreshed data for ${musicId}:`, refreshedMusic.cover);

        // Add timestamp to break cache
        const coverUrl = `${refreshedMusic.cover}?t=${new Date().getTime()}`;

        // Update coverImages state
        setCoverImages(prev => ({
          ...prev,
          [musicId]: coverUrl
        }));

        // Update music list
        setMusics(prev =>
          prev.map(music =>
            music._id === musicId
              ? { ...refreshedMusic, cover: coverUrl, coverGenerating: false }
              : music
          )
        );

        // Remove from generating set
        setGeneratingCovers(prev => {
          const updated = new Set(prev);
          updated.delete(musicId);
          return updated;
        });

        // Force re-render
        setForceUpdate(prev => !prev);
      } else {
        // If still no cover, check again after 10 seconds
        setTimeout(() => {
          checkCoverDirectly(musicId);
        }, 10000);
      }
    } catch (error) {
      console.error(`Error in direct check for cover of ${musicId}:`, error);
    }
  };

  const pollMusicStatus = async (musicId: string) => {
    try {
      console.log(`Polling music status for ${musicId}`);
      const updatedMusic = await musicService.getMusic(musicId);

      console.log(`Music status for ${musicId}:`, updatedMusic.status);

      // Update the music in the list
      setMusics(prevMusics =>
        prevMusics.map(music =>
          music._id === musicId ? updatedMusic : music
        )
      );

      // Update cover images if new cover is available
      if (updatedMusic.cover && !coverImages[musicId]) {
        setCoverImages(prev => ({
          ...prev,
          [musicId]: updatedMusic.cover
        }));
      }

      // If music is no longer pending, remove from polling IDs
      if (!['PENDING', 'GENERATING_LYRICS', 'LYRICS_GENERATED', 'GENERATING_MUSIC', 'MUSIC_GENERATED', 'FIRST_TRACK_GENERATED', 'GENERATING_COVER', 'COVER_GENERATED', 'TEXT_SUCCESS', 'FIRST_SUCCESS', 'pending'].includes(updatedMusic.status.toUpperCase())) {
        setPollingIds(prev => {
          const updated = new Set(prev);
          updated.delete(musicId);
          return updated;
        });
      }
    } catch (error) {
      console.error(`Error polling music status for ${musicId}:`, error);
    }
  };

  useEffect(() => {
    loadMusics();
  }, []);

  // Polling effect
  useEffect(() => {
    if (pollingIds.size === 0) return;

    console.log(`Setting up polling for ${pollingIds.size} music items:`, Array.from(pollingIds));

    const intervals: NodeJS.Timeout[] = [];

    pollingIds.forEach((id) => {
      const interval = setInterval(() => {
        pollMusicStatus(id);
      }, 5000); // Poll every 5 seconds
      intervals.push(interval);
    });

    return () => {
      intervals.forEach(clearInterval);
    };
  }, [pollingIds]);

  const handlePlayClick = (music: Music, version: number = 1) => {
    // Check tracks first, then fallback to audioUrl
    if (music.tracks && music.tracks.length > 0) {
      const track = music.tracks[version - 1];
      if (track && track.audioUrl) {
        setCurrentMusic(music);
        setCurrentVersion(version);
        setIsPlaying(true);
      }
    } else if (music.audioUrl) {
      setCurrentMusic(music);
      setCurrentVersion(version);
      setIsPlaying(true);
    }
  };

  const handleDownloadClick = (music: Music) => {
    // Check if music has multiple versions
    if (music.tracks && music.tracks.length > 1) {
      setSelectedMusicForDownload(music);
      setDownloadModalOpen(true);
    } else {
      // Direct download for single version
      handleDownload(music, 1);
    }
  };

  const handleDownload = async (music: Music, version: number = 1) => {
    try {
      setDownloadingId(music._id);
      setDownloadingVersion(version);
      setDownloadModalOpen(false); // Close modal if open

      // Check tracks first, then fallback to audioUrl
      let audioUrl = '';
      if (music.tracks && music.tracks.length > 0) {
        audioUrl = music.tracks[version - 1]?.audioUrl || '';
      } else {
        audioUrl = music.audioUrl || '';
      }

      // Ensure we have a valid URL
      if (!audioUrl) {
        console.error('No audio URL provided');
        alert('Tidak ada URL audio yang tersedia untuk diunduh.');
        return;
      }

      // Add API_URL if the URL is relative (doesn't start with http or https)
      const fullUrl = audioUrl.startsWith('http') ? audioUrl : `${API_URL}${audioUrl}`;
      console.log('Downloading from:', fullUrl);

      try {
        // Use XMLHttpRequest instead of fetch for better cross-origin support
        const xhr = new XMLHttpRequest();
        xhr.open('GET', fullUrl, true);
        xhr.responseType = 'blob';

        xhr.onload = function () {
          if (this.status === 200) {
            const blob = new Blob([this.response], { type: 'audio/mpeg' });
            const downloadUrl = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = `${music.title} - Versi ${version}.mp3`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(downloadUrl);
          } else {
            console.error(`HTTP error! status: ${this.status}`);
            alert(`Gagal mengunduh lagu (Status: ${this.status}). Silakan coba lagi nanti.`);
          }
        };

        xhr.onerror = function () {
          console.error('Network error occurred');
          alert('Terjadi kesalahan jaringan saat mengunduh lagu. Silakan coba lagi nanti.');
        };

        xhr.send();
      } catch (error) {
        console.error('Error downloading file:', error);
        alert('Gagal mengunduh lagu. Silakan coba lagi nanti.');
      }
    } catch (error) {
      console.error('Error in download handler:', error);
      alert('Terjadi kesalahan saat mengunduh lagu. Silakan coba lagi nanti.');
    } finally {
      setDownloadingId('');
      setDownloadingVersion(0);
    }
  };

  const handleNavigateToDetail = (musicId: string, event: React.MouseEvent) => {
    // Prevent navigation if the click was on a button or dropdown
    if (
      event.target instanceof HTMLElement &&
      (event.target.closest('button') ||
        event.target.closest('.dropdown') ||
        event.target.tagName === 'BUTTON' ||
        event.target.tagName === 'SELECT')
    ) {
      return;
    }

    navigate(`/music/${musicId}`);
  };

  const renderMusicCard = (music: Music) => {
    // Check for multiple versions in tracks
    const hasMultipleVersions = (music.tracks && music.tracks.length > 1);

    // Check for audio availability in tracks first, then audioUrl
    const hasAudio = (music.tracks && music.tracks.some(track => track.audioUrl)) ||
                     music.audioUrl;

    const currentTrack = music.tracks?.[0];
    const isPlayable = ['SUCCESS', 'completed', 'succeeded'].includes(music.status.toUpperCase()) && hasAudio;
    const coverToDisplay = coverImages[music._id] || music.cover || currentTrack?.imageUrl || defaultCoverImage;


    
    // Check if music is in generation process
    const isGenerating = ['PENDING', 'GENERATING_LYRICS', 'LYRICS_GENERATED', 'GENERATING_MUSIC', 'MUSIC_GENERATED', 'FIRST_TRACK_GENERATED', 'GENERATING_COVER', 'COVER_GENERATED', 'TEXT_SUCCESS', 'FIRST_SUCCESS', 'pending'].includes(music.status.toUpperCase());
    
    // Use generating card for music in progress
    if (isGenerating) {
      return <GeneratingMusicCard key={music._id} music={music} />;
    }

    return (
      <div
        key={music._id}
        className={`group bg-gradient-to-br from-base-100 to-base-200/50 border border-base-300/50 rounded-2xl overflow-hidden transition-all duration-300 hover:shadow-xl hover:shadow-primary/10 hover:border-primary/30 hover:-translate-y-1 ${viewMode === 'list' ? 'flex flex-col md:flex-row' : 'h-full flex flex-col'} cursor-pointer relative`}
        onClick={(e) => handleNavigateToDetail(music._id, e)}
      >
        {/* Cover Image Section */}
        <div className={`${viewMode === 'list' ? 'md:w-48 md:h-32' : 'w-full aspect-[4/3]'} relative overflow-hidden flex-shrink-0`}>
          <img
            src={coverToDisplay}
            alt={music.title}
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
          />

          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

          {/* Status Badge */}
          <div className="absolute top-3 left-3">
            <div className={`badge ${getStatusColor(music.status)} badge-sm font-medium shadow-lg backdrop-blur-sm`}>
              {getStatusDisplay(music.status)}
            </div>
          </div>

          {/* Action Buttons Overlay */}
          <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
            <div className="flex gap-2">
              {isPlayable && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handlePlayClick(music, 1);
                  }}
                  className="btn btn-circle btn-primary btn-lg shadow-xl hover:scale-110 transition-transform duration-200"
                  title="Putar"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" viewBox="0 0 24 24" fill="currentColor">
                    <polygon points="5 3 19 12 5 21 5 3"></polygon>
                  </svg>
                </button>
              )}
            </div>
          </div>

          {/* Cover Generation Loading */}
          {(generatingCovers.has(music._id) || music.coverGenerating) && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/70 backdrop-blur-sm">
              <div className="text-center text-white">
                <div className="loading loading-spinner loading-lg text-primary mb-2"></div>
                <p className="text-sm font-medium">Membuat Cover...</p>
              </div>
            </div>
          )}


        </div>

        {/* Content Section */}
        <div className={`p-4 ${viewMode === 'list' ? 'flex-1' : 'flex-1'} flex flex-col`}>
          {/* Header - Compact Section */}
          <div className="flex-shrink-0 mb-3">
            <div className="flex items-start justify-between mb-2">
              <h3 className="text-lg font-bold text-base-content line-clamp-2 group-hover:text-primary transition-colors duration-200 flex-1 pr-2">
                {music.title}
              </h3>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setIsModalOpen(true);
                  setSelectedMusic(music);
                }}
                className="btn btn-ghost btn-xs btn-circle opacity-60 hover:opacity-100 hover:bg-primary/10 hover:text-primary transition-all duration-200 flex-shrink-0"
                title="Bagikan"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-3 h-3">
                  <circle cx="18" cy="5" r="3"></circle>
                  <circle cx="6" cy="12" r="3"></circle>
                  <circle cx="18" cy="19" r="3"></circle>
                  <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
                  <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
                </svg>
              </button>
            </div>

            <p className="text-xs text-base-content/70 line-clamp-2 mb-2">
              {music.prompt}
            </p>

            {/* Tags - Compact */}
            <div className="mb-2">
              {music.tags && (
                <div className="flex flex-wrap gap-1">
                  {music.tags.split(',').slice(0, 2).map((tag, index) => (
                    <span key={index} className="badge badge-outline badge-xs text-xs">
                      {tag.trim()}
                    </span>
                  ))}
                  {music.tags.split(',').length > 2 && (
                    <span className="badge badge-ghost badge-xs text-xs">
                      +{music.tags.split(',').length - 2}
                    </span>
                  )}
                </div>
              )}
            </div>

            {/* Date */}
            <p className="text-xs text-base-content/50 mb-3">
              {new Date(music.createdAt).toLocaleDateString('id-ID', {
                day: 'numeric',
                month: 'short',
                year: 'numeric'
              })}
            </p>
          </div>

          {/* Middle Section - Compact Lyrics */}
          <div className="flex-1 flex flex-col">
            {/* Lyrics Preview - Smaller */}
            {currentClip?.lyrics && (
              <div className="mb-3">
                <div className="p-2 bg-base-200/50 rounded-lg">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs font-medium text-base-content/70">LIRIK</span>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedLyrics(currentClip?.lyrics || '');
                        setSelectedTitle(music.title);
                        setShowLyricsModal(true);
                      }}
                      className="text-xs text-primary hover:underline"
                    >
                      Lihat
                    </button>
                  </div>
                  <p className="text-xs text-base-content/80 line-clamp-2 italic">
                    {currentClip.lyrics}
                  </p>
                </div>
              </div>
            )}

            {/* Spacer to push buttons to bottom */}
            <div className="flex-1"></div>

            {/* Action Buttons - Compact */}
            <div className="flex-shrink-0">
              <div className="flex flex-col gap-2">
                {/* Version Selector for Multiple Versions - Compact */}
                {hasMultipleVersions && isPlayable && (
                  <div className="flex gap-1">
                    {music.tracks?.slice(0, 2).map((track, index) => (
                      <button
                        key={index}
                        onClick={(e) => {
                          e.stopPropagation();
                          handlePlayClick(music, index + 1);
                        }}
                        className="flex-1 btn btn-xs btn-outline hover:btn-primary transition-all duration-200"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 24 24" fill="currentColor">
                          <polygon points="5 3 19 12 5 21 5 3"></polygon>
                        </svg>
                        V{index + 1}
                      </button>
                    ))}
                    {music.tracks && music.tracks.length > 2 && (
                      <div className="dropdown dropdown-top">
                        <div tabIndex={0} role="button" className="btn btn-xs btn-outline">
                          +{music.tracks.length - 2}
                        </div>
                        <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-24">
                          {music.tracks.slice(2).map((track, index) => (
                            <li key={index + 2}>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handlePlayClick(music, index + 3);
                                }}
                                className="text-xs"
                              >
                                V{index + 3}
                              </button>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                )}

                {/* Generate Cover Button - Only show if no cover exists and music is completed */}
                {['SUCCESS', 'completed', 'succeeded'].includes(music.status.toUpperCase()) &&
                  !music.cover &&
                  !coverImages[music._id] && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleGenerateCover(music._id);
                      }}
                      disabled={generatingCovers.has(music._id) || music.coverGenerating}
                      className={`btn btn-outline btn-xs gap-1 transition-all duration-200 ${generatingCovers.has(music._id) || music.coverGenerating
                        ? 'border-warning text-warning cursor-not-allowed'
                        : 'border-secondary text-secondary hover:bg-secondary hover:text-secondary-content hover:border-secondary'
                        }`}
                      title="Generate Cover dengan AI"
                    >
                      {generatingCovers.has(music._id) || music.coverGenerating ? (
                        <>
                          <div className="loading loading-spinner loading-xs"></div>
                          <span className="text-xs">Membuat...</span>
                        </>
                      ) : (
                        <>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
                            <circle cx="9" cy="9" r="2" />
                            <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21" />
                          </svg>
                          <span className="text-xs">AI Cover</span>
                        </>
                      )}
                    </button>
                  )}

                {/* Main Actions - Compact */}
                <div className="flex gap-1">
                  {isPlayable && (
                    <>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handlePlayClick(music, 1);
                        }}
                        className="flex-1 btn btn-primary btn-sm gap-1 hover:scale-105 transition-transform duration-200"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 24 24" fill="currentColor">
                          <polygon points="5 3 19 12 5 21 5 3"></polygon>
                        </svg>
                        <span className="text-xs">Putar</span>
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDownloadClick(music);
                        }}
                        disabled={downloadingId === music._id}
                        className="btn btn-outline btn-sm hover:scale-105 transition-transform duration-200"
                        title="Unduh"
                      >
                        {downloadingId === music._id ? (
                          <div className="loading loading-spinner loading-xs"></div>
                        ) : (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4" />
                            <polyline points="7 10 12 15 17 10" />
                            <line x1="12" y1="15" x2="12" y2="3" />
                          </svg>
                        )}
                      </button>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <span className="loading loading-spinner loading-lg"></span>
      </div>
    );
  }

  return (
    <div className="container-custom p-4 lg:p-6 max-w-7xl mx-auto">
      {/* Add the success modal */}
      <dialog id="success_generate_cover_modal" className="modal">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Cover Generation Started!</h3>
          <p className="pt-4 pb-2">Cover musik Anda sedang dibuat. Anda dapat menutup pesan ini dan menunggu hasilnya.</p>
          <div className="modal-action">
            <form method="dialog">
              <button className="btn">Close</button>
            </form>
          </div>
        </div>
      </dialog>

      {/* Header Section */}
      <div className="mb-4 lg:mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center gap-4 mb-6">
          <div className="flex items-center gap-3">
            <div className="p-2 sm:p-3 bg-primary/10 rounded-lg">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 sm:h-8 sm:w-8 text-primary" viewBox="0 0 20 20" fill="currentColor">
                <path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v9.114A4.369 4.369 0 005 14c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V7.82l8-1.6v5.894A4.37 4.37 0 0015 12c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V3z" />
              </svg>
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold">Riwayat Musik</h1>
              <p className="text-sm sm:text-base text-base-content/60">Kelola dan putar musik yang telah Anda buat</p>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 mb-4">
          <div className="bg-base-100 border border-base-200 rounded-xl p-3 sm:p-4">
            <div className="text-xs sm:text-sm text-base-content/60 mb-1">Total Musik</div>
            <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-primary">{stats.total}</div>
          </div>
          <div className="bg-base-100 border border-base-200 rounded-xl p-3 sm:p-4">
            <div className="text-xs sm:text-sm text-base-content/60 mb-1">Selesai</div>
            <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-success">{stats.completed}</div>
          </div>
          <div className="bg-base-100 border border-base-200 rounded-xl p-3 sm:p-4">
            <div className="text-xs sm:text-sm text-base-content/60 mb-1">Sedang Diproses</div>
            <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-warning">{stats.pending}</div>
          </div>
          <div className="bg-base-100 border border-base-200 rounded-xl p-3 sm:p-4">
            <div className="text-xs sm:text-sm text-base-content/60 mb-1">Gagal</div>
            <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-error">{stats.failed}</div>
          </div>
        </div>

        {/* Search, Filter, and View Controls */}
        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
          <div className="flex-1">
            <div className="relative">
              <input
                type="text"
                placeholder="Cari berdasarkan judul, prompt, atau tag..."
                className="input input-bordered w-full pl-10 bg-base-100 text-sm"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 absolute left-3 top-1/2 -translate-y-1/2 text-base-content/40" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" />
              </svg>
            </div>
          </div>
          <div className="flex gap-2 flex-col sm:flex-row">
            <select
              className="select select-bordered w-full sm:w-48 bg-base-100 text-sm"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">Semua Status</option>
              <option value="completed">Selesai</option>
              <option value="pending">Sedang Diproses</option>
              <option value="failed">Gagal</option>
            </select>
            <select
              className="select select-bordered w-full sm:w-48 bg-base-100 text-sm"
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'newest' | 'oldest' | 'title' | 'status')}
            >
              <option value="newest">Terbaru</option>
              <option value="oldest">Terlama</option>
              <option value="title">Judul A-Z</option>
              <option value="status">Status</option>
            </select>
            <div className="join">
              <button
                className={`btn btn-sm join-item ${viewMode === 'grid' ? 'btn-active' : ''}`}
                onClick={() => setViewMode('grid')}
                title="Grid View"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
              </button>
              <button
                className={`btn btn-sm join-item ${viewMode === 'list' ? 'btn-active' : ''}`}
                onClick={() => setViewMode('list')}
                title="List View"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Music List Grid/List */}
        <div className={`grid gap-3 mt-6 ${viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' : 'grid-cols-1'}`}>
          {/* Music List */}
          <div className="space-y-3 sm:space-y-4 min-h-[500px] col-span-full">
            {filteredAndSortedMusics.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-[300px] sm:h-[500px] bg-base-100 border border-base-200 rounded-xl p-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 sm:h-16 sm:w-16 text-base-content/40 mb-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <p className="text-sm sm:text-base text-base-content/60">Belum ada musik yang dibuat</p>
              </div>
            ) : (
              <div className={`grid ${viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' : 'grid-cols-1'} gap-3`}>
                {filteredAndSortedMusics.map((music) => renderMusicCard(music))}
              </div>
            )}
          </div>
        </div>

        {/* Lirik Modal */}
        {showLyricsModal && (
          <div className="fixed inset-0 z-40">
            <div className="flex items-start justify-center h-[calc(100vh-80px)] p-4">
              {/* Backdrop */}
              <div
                className="fixed inset-0 bg-black/60 backdrop-blur-sm transition-opacity"
                onClick={() => setShowLyricsModal(false)}
              />

              {/* Modal Content */}
              <div className="relative bg-base-100 rounded-2xl max-w-lg w-full shadow-2xl mt-8 border border-base-200">
                {/* Header */}
                <div className="flex items-center justify-between px-6 py-4 border-b border-base-200 bg-base-100/50 backdrop-blur supports-[backdrop-filter]:bg-base-100/50 rounded-t-2xl">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v9.114A4.369 4.369 0 005 14c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V7.82l8-1.6v5.894A4.37 4.37 0 0015 12c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V3z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold leading-none mb-1">{selectedTitle}</h3>
                      <p className="text-xs text-base-content/60">Lirik Lagu</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={handleCopyLyrics}
                      className="btn btn-ghost btn-sm gap-2 hover:bg-base-200 transition-colors"
                      title="Salin lirik"
                    >
                      {isCopied ? (
                        <>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-success" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                          <span className="text-success">Tersalin!</span>
                        </>
                      ) : (
                        <>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M7 9a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H9a2 2 0 01-2-2V9z" />
                            <path d="M5 3a2 2 0 00-2 2v6a2 2 0 002 2V5h8a2 2 0 00-2-2H5z" />
                          </svg>
                          <span>Salin</span>
                        </>
                      )}
                    </button>
                    <button
                      onClick={() => setShowLyricsModal(false)}
                      className="btn btn-ghost btn-sm btn-square hover:bg-base-200 hover:text-error transition-colors"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </div>
                </div>

                {/* Content */}
                <div className="p-6 overflow-y-auto bg-base-100 rounded-b-2xl" style={{ maxHeight: 'calc(100vh - 250px)' }}>
                  <div className="prose prose-sm max-w-none">
                    <div className="bg-base-200/50 rounded-xl p-6">
                      <p className="whitespace-pre-wrap text-base-content/90 leading-relaxed">{selectedLyrics}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Download Version Selection Modal */}
        {downloadModalOpen && selectedMusicForDownload && (
          <div className="fixed inset-0 z-50 flex items-center justify-center">
            <div
              className="fixed inset-0 bg-black/60 backdrop-blur-sm transition-opacity"
              onClick={() => setDownloadModalOpen(false)}
            />
            <div className="relative bg-base-100 rounded-2xl max-w-md w-full mx-4 shadow-2xl border border-base-200">
              {/* Header */}
              <div className="flex items-center justify-between px-6 py-4 border-b border-base-200">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4" />
                      <polyline points="7 10 12 15 17 10" />
                      <line x1="12" y1="15" x2="12" y2="3" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">Pilih Versi</h3>
                    <p className="text-xs text-base-content/60">Pilih versi yang ingin diunduh</p>
                  </div>
                </div>
                <button
                  onClick={() => setDownloadModalOpen(false)}
                  className="btn btn-ghost btn-sm btn-square hover:bg-base-200"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>

              {/* Content */}
              <div className="p-6">
                <div className="mb-4">
                  <h4 className="font-medium text-base-content mb-2">{selectedMusicForDownload.title}</h4>
                  <p className="text-sm text-base-content/60">Tersedia {selectedMusicForDownload.clips?.length || 1} versi</p>
                </div>

                <div className="space-y-3">
                  {selectedMusicForDownload.clips?.map((clip, index) => (
                    <button
                      key={index}
                      onClick={() => handleDownload(selectedMusicForDownload, index + 1)}
                      disabled={downloadingId === selectedMusicForDownload._id && downloadingVersion === index + 1}
                      className="w-full flex items-center justify-between p-4 bg-base-200/50 hover:bg-base-200 rounded-xl transition-colors duration-200 border border-transparent hover:border-primary/20"
                    >
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-primary/10 rounded-lg">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-primary" viewBox="0 0 24 24" fill="currentColor">
                            <polygon points="5 3 19 12 5 21 5 3"></polygon>
                          </svg>
                        </div>
                        <div className="text-left">
                          <p className="font-medium">Versi {index + 1}</p>
                          <p className="text-xs text-base-content/60">
                            {Math.floor((clip.duration || 0) / 60)}:{String(Math.floor((clip.duration || 0) % 60)).padStart(2, '0')}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {downloadingId === selectedMusicForDownload._id && downloadingVersion === index + 1 ? (
                          <div className="loading loading-spinner loading-sm"></div>
                        ) : (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-base-content/60" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4" />
                            <polyline points="7 10 12 15 17 10" />
                            <line x1="12" y1="15" x2="12" y2="3" />
                          </svg>
                        )}
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Share Music Modal */}
        {isModalOpen && selectedMusic && (
          <div className="fixed inset-0 z-40 flex items-center justify-center">
            <div
              className="fixed inset-0 bg-black/60 backdrop-blur-sm transition-opacity"
              onClick={() => setIsModalOpen(false)}
            />
            <div className="relative z-50">
              <ShareMusicModal musicId={selectedMusic._id} />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
