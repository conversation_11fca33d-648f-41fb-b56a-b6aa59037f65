"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var BullBoardService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.BullBoardService = void 0;
const common_1 = require("@nestjs/common");
const bull_1 = require("@nestjs/bull");
const api_1 = require("@bull-board/api");
const bullAdapter_1 = require("@bull-board/api/bullAdapter");
const express_1 = require("@bull-board/express");
const config_1 = require("@nestjs/config");
const basicAuth = require("express-basic-auth");
const constant_1 = require("./constant");
let BullBoardService = BullBoardService_1 = class BullBoardService {
    constructor(generateMusicQueue, generateCoverQueue, uploadFileQueue, pollingQueue, configService) {
        this.generateMusicQueue = generateMusicQueue;
        this.generateCoverQueue = generateCoverQueue;
        this.uploadFileQueue = uploadFileQueue;
        this.pollingQueue = pollingQueue;
        this.configService = configService;
        this.logger = new common_1.Logger(BullBoardService_1.name);
        this.username = this.configService.get('BULL_BOARD_USERNAME') || 'admin';
        this.password = this.configService.get('BULL_BOARD_PASSWORD') || 'admin';
        this.logger.log('Initializing Bull Board with authentication');
        BullBoardService_1.adapter = new express_1.ExpressAdapter();
        BullBoardService_1.adapter.setBasePath('/admin/queues');
        (0, api_1.createBullBoard)({
            queues: [
                new bullAdapter_1.BullAdapter(this.generateMusicQueue),
                new bullAdapter_1.BullAdapter(this.generateCoverQueue),
                new bullAdapter_1.BullAdapter(this.uploadFileQueue),
                new bullAdapter_1.BullAdapter(this.pollingQueue),
            ],
            serverAdapter: BullBoardService_1.adapter,
        });
    }
    getRouter() {
        const express = require('express');
        const router = express.Router();
        router.use(basicAuth({
            users: { [this.username]: this.password },
            challenge: true,
            realm: 'Bull Board',
        }));
        this.logger.log(`Bull Board secured with username: ${this.username}`);
        router.use('/', BullBoardService_1.adapter.getRouter());
        return router;
    }
};
exports.BullBoardService = BullBoardService;
exports.BullBoardService = BullBoardService = BullBoardService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, bull_1.InjectQueue)(constant_1.QUEUE_GENERATE_MUSIC)),
    __param(1, (0, bull_1.InjectQueue)(constant_1.QUEUE_GENERATE_COVER)),
    __param(2, (0, bull_1.InjectQueue)(constant_1.QUEUE_UPLOAD_FILE)),
    __param(3, (0, bull_1.InjectQueue)(constant_1.QUEUE_POLLING)),
    __metadata("design:paramtypes", [Object, Object, Object, Object, config_1.ConfigService])
], BullBoardService);
//# sourceMappingURL=bull-board.service.js.map