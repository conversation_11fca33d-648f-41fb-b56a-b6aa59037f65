import { CommandParser } from '../client/parser';
import { RedisArgument, BlobStringReply, NullReply } from '../RESP/types';
declare const _default: {
    /**
     * Constructs the RPOP command
     *
     * @param parser - The command parser
     * @param key - The list key to pop from
     * @see https://redis.io/commands/rpop/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument) => void;
    readonly transformReply: () => BlobStringReply | NullReply;
};
export default _default;
//# sourceMappingURL=RPOP.d.ts.map