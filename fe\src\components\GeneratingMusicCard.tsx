import { useState, useEffect } from 'react';
import { Music } from '../types';

interface GeneratingMusicCardProps {
  music: Music;
}

// Simple progress bar with animated stripes
const ProgressBar = ({ stage }: { stage: number }) => {
  const progress = stage === 0 ? 10 :
                   stage === 1 ? 25 :
                   stage === 2 ? 40 :
                   stage === 3 ? 60 :
                   stage === 4 ? 75 :
                   stage === 5 ? 85 :
                   stage === 6 ? 95 : 100;

  return (
    <div className="w-full bg-base-300 rounded-full h-2 overflow-hidden">
      <div
        className="h-full bg-gradient-to-r from-primary to-secondary rounded-full transition-all duration-1000 ease-out relative"
        style={{ width: `${progress}%` }}
      >
        {/* Animated stripe overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
      </div>
    </div>
  );
};

export default function GeneratingMusicCard({ music }: GeneratingMusicCardProps) {
  const [timeElapsed, setTimeElapsed] = useState(0);
  
  // Timer for elapsed time
  useEffect(() => {
    const interval = setInterval(() => {
      setTimeElapsed(prev => prev + 1);
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Format time
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Determine current stage
  const getStageInfo = (status: string) => {
    switch (status.toUpperCase()) {
      case 'PENDING':
        return { stage: 0, label: 'Memulai generasi...', icon: '🚀' };
      case 'GENERATING_LYRICS':
        return { stage: 1, label: 'Membuat lirik...', icon: '✍️' };
      case 'LYRICS_GENERATED':
        return { stage: 2, label: 'Lirik selesai, mempersiapkan audio...', icon: '📝' };
      case 'GENERATING_MUSIC':
        return { stage: 3, label: 'Mengkomposisi audio...', icon: '🎵' };
      case 'MUSIC_GENERATED':
        return { stage: 4, label: 'Audio selesai, membuat track tambahan...', icon: '🎶' };
      case 'FIRST_TRACK_GENERATED':
        return { stage: 5, label: 'Track pertama selesai...', icon: '🎼' };
      case 'GENERATING_COVER':
        return { stage: 6, label: 'Membuat cover art...', icon: '🎨' };
      case 'COVER_GENERATED':
        return { stage: 7, label: 'Cover selesai, finalisasi...', icon: '✨' };
      // Legacy status support
      case 'TEXT_SUCCESS':
        return { stage: 1, label: 'Membuat lirik...', icon: '✍️' };
      case 'FIRST_SUCCESS':
        return { stage: 3, label: 'Mengkomposisi audio...', icon: '🎵' };
      default:
        return { stage: 0, label: 'Memproses...', icon: '⏳' };
    }
  };

  const stageInfo = getStageInfo(music.status);

  return (
    <div className="relative bg-gradient-to-br from-base-100 to-base-200/50 border-2 border-primary/20 rounded-2xl overflow-hidden shadow-lg h-full flex flex-col">
      {/* Animated border light that travels around the card - Much more visible */}
      <div className="absolute inset-0 rounded-2xl overflow-hidden">
        {/* Top border light - Bigger */}
        <div className="absolute top-0 left-0 w-32 h-2 bg-gradient-to-r from-transparent via-primary to-transparent animate-border-top shadow-2xl shadow-primary/80"></div>
        {/* Right border light - Bigger */}
        <div className="absolute top-0 right-0 w-2 h-32 bg-gradient-to-b from-transparent via-primary to-transparent animate-border-right shadow-2xl shadow-primary/80"></div>
        {/* Bottom border light - Bigger */}
        <div className="absolute bottom-0 right-0 w-32 h-2 bg-gradient-to-l from-transparent via-primary to-transparent animate-border-bottom shadow-2xl shadow-primary/80"></div>
        {/* Left border light - Bigger */}
        <div className="absolute bottom-0 left-0 w-2 h-32 bg-gradient-to-t from-transparent via-primary to-transparent animate-border-left shadow-2xl shadow-primary/80"></div>
        
        {/* Additional glow effect - Much bigger */}
        <div className="absolute top-0 left-0 w-40 h-4 bg-gradient-to-r from-transparent via-primary/50 to-transparent animate-border-top blur-md"></div>
        <div className="absolute top-0 right-0 w-4 h-40 bg-gradient-to-b from-transparent via-primary/50 to-transparent animate-border-right blur-md"></div>
        <div className="absolute bottom-0 right-0 w-40 h-4 bg-gradient-to-l from-transparent via-primary/50 to-transparent animate-border-bottom blur-md"></div>
        <div className="absolute bottom-0 left-0 w-4 h-40 bg-gradient-to-t from-transparent via-primary/50 to-transparent animate-border-left blur-md"></div>
      </div>
      
      {/* Content - Centered vertically with better spacing */}
      <div className="relative flex-1 flex flex-col justify-center p-4 space-y-4">
        {/* Header - Better spacing */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-primary" viewBox="0 0 20 20" fill="currentColor">
                <path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v9.114A4.369 4.369 0 005 14c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V7.82l8-1.6v5.894A4.37 4.37 0 0015 12c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V3z" />
              </svg>
            </div>
            <div>
              <h3 className="font-bold text-base-content text-sm line-clamp-1">{music.title}</h3>
              <p className="text-xs text-base-content/60 mt-0.5">Sedang dibuat</p>
            </div>
          </div>
          <div className="flex items-center">
            {/* Soundwave animation */}
            <div className="flex items-center gap-0.5 h-6">
              {[...Array(6)].map((_, i) => (
                <div
                  key={i}
                  className="bg-primary rounded-full animate-soundwave"
                  style={{
                    width: '3px',
                    height: `${12 + (i % 3) * 6}px`,
                    animationDelay: `${i * 0.15}s`,
                    animationDuration: `${0.8 + (i % 2) * 0.2}s`
                  }}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Progress Section - Better spacing */}
        <div className="flex items-center gap-3">
          <span className="text-lg">{stageInfo.icon}</span>
          <div className="flex-1">
            <p className="text-xs font-medium text-base-content mb-2">{stageInfo.label}</p>
            <ProgressBar stage={stageInfo.stage} />
          </div>
        </div>

        {/* Music Info - Better spacing */}
        <div className="bg-base-200/50 rounded-lg p-3 space-y-2">
          <p className="text-xs text-base-content/70 line-clamp-2">{music.prompt}</p>
          
          <div className="flex items-center justify-between pt-1">
            {/* Tags */}
            {music.tags && (
              <div className="flex flex-wrap gap-1">
                {music.tags.split(',').slice(0, 2).map((tag, index) => (
                  <span key={index} className="badge badge-primary badge-xs">
                    {tag.trim()}
                  </span>
                ))}
              </div>
            )}
            
            {/* Estimated Time - Inline */}
            <div className="inline-flex items-center gap-1 px-2 py-1 bg-warning/10 text-warning rounded-full text-xs">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
              </svg>
              <span>1-3 min</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}