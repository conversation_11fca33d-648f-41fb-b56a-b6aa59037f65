import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
/**
 * Command for getting the number of members in a sorted set
 */
declare const _default: {
    readonly CACHEABLE: true;
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the ZCARD command to get the cardinality (number of members) of a sorted set
     *
     * @param parser - The command parser
     * @param key - The sorted set key
     * @returns Number of members in the sorted set
     * @see https://redis.io/commands/zcard/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=ZCARD.d.ts.map