import React, { useEffect, useRef, useState } from 'react';
import { useMusicPlayer } from '../contexts/MusicPlayerContext';
import { FaPlay, FaPause, FaStepForward, FaStepBackward, FaTimes } from 'react-icons/fa';
import { formatTime } from '../utils/formatTime';

export function PersistentPlayer() {
  const {
    currentMusic,
    isPlaying,
    currentVersion,
    setIsPlaying,
    setCurrentVersion,
    clearCurrentMusic,
  } = useMusicPlayer();
  
  const audioRef = useRef<HTMLAudioElement>(null);
  const [progress, setProgress] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.play();
      } else {
        audioRef.current.pause();
      }
    }
  }, [isPlaying, currentMusic, currentVersion]);

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      const progress = (audioRef.current.currentTime / audioRef.current.duration) * 100;
      setProgress(progress);
    }
  };

  const handleLoadedMetadata = () => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration);
    }
  };

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (audioRef.current) {
      const progressBar = e.currentTarget;
      const clickPosition = e.clientX - progressBar.getBoundingClientRect().left;
      const progressBarWidth = progressBar.clientWidth;
      const percentage = (clickPosition / progressBarWidth) * 100;
      const newTime = (percentage / 100) * audioRef.current.duration;
      audioRef.current.currentTime = newTime;
      setProgress(percentage);
    }
  };

  const handleVersionChange = (increment: number) => {
    // Check tracks first, then clips
    const totalVersions = currentMusic?.tracks?.length || currentMusic?.clips?.length || 1;
    if (!currentMusic || totalVersions <= 1) return;

    const newVersion = currentVersion + increment;
    if (newVersion >= 1 && newVersion <= totalVersions) {
      setCurrentVersion(newVersion);
    }
  };

  if (!currentMusic || (!currentMusic.tracks && !currentMusic.clips && !currentMusic.audioUrl)) return null;

  // Get audio URL from tracks first, then clips, then audioUrl
  const currentAudioUrl = currentMusic.tracks && currentMusic.tracks.length > 0
    ? currentMusic.tracks[currentVersion - 1]?.audioUrl || ''
    : currentMusic.clips && currentMusic.clips.length > 0
    ? currentMusic.clips[currentVersion - 1]?.audio_url || ''
    : currentMusic.audioUrl || '';

  if (!currentAudioUrl) return null;

  return (
    <div 
      className="fixed bottom-0 left-0 right-0 lg:left-64 bg-base-100 border-t border-base-200 text-base-content backdrop-blur-md bg-opacity-80"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <audio
        ref={audioRef}
        src={currentAudioUrl}
        onTimeUpdate={handleTimeUpdate}
        onLoadedMetadata={handleLoadedMetadata}
        onEnded={() => setIsPlaying(false)}
      />
      
      {/* Progress Bar - Full Width */}
      <div 
        className="relative h-1 bg-base-200 cursor-pointer transition-all duration-300 group hover:h-2"
        onClick={handleProgressClick}
      >
        <div
          className="absolute top-0 left-0 h-full bg-primary transition-all duration-300"
          style={{ width: `${progress}%` }}
        />
        <div 
          className="absolute top-1/2 -translate-y-1/2 h-3 w-3 rounded-full bg-primary opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          style={{ 
            left: `${progress}%`,
            transform: `translate(-50%, -50%)`
          }}
        />
      </div>

      <div className="container mx-auto max-w-6xl p-3">
        <div className="flex items-center gap-4">
          {/* Album Art */}
          <div className="w-12 h-12 bg-base-200 rounded-lg flex items-center justify-center overflow-hidden">
            <div className="w-full h-full bg-primary/10 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary/40" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
              </svg>
            </div>
          </div>

          {/* Player Controls Section */}
          <div className="flex-1">
            <div className="flex items-center justify-between">
              {/* Song Info */}
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{currentMusic.title}</p>
                {currentMusic.clips && currentMusic.clips.length > 1 && (
                  <p className="text-xs text-base-content/60">Version {currentVersion} of {currentMusic.clips.length}</p>
                )}
              </div>

              {/* Controls */}
              <div className="flex items-center gap-2">
                {currentMusic.clips && currentMusic.clips.length > 1 && (
                  <button
                    onClick={() => handleVersionChange(-1)}
                    disabled={currentVersion === 1}
                    className="btn btn-sm btn-circle btn-ghost text-base-content/60 hover:text-base-content disabled:opacity-50"
                  >
                    <FaStepBackward className="w-3 h-3" />
                  </button>
                )}
                
                <button
                  onClick={() => setIsPlaying(!isPlaying)}
                  className="btn btn-circle btn-sm btn-primary"
                >
                  {isPlaying ? <FaPause className="w-3 h-3" /> : <FaPlay className="w-3 h-3" />}
                </button>

                {currentMusic.clips && currentMusic.clips.length > 1 && (
                  <button
                    onClick={() => handleVersionChange(1)}
                    disabled={currentVersion === currentMusic.clips.length}
                    className="btn btn-sm btn-circle btn-ghost text-base-content/60 hover:text-base-content disabled:opacity-50"
                  >
                    <FaStepForward className="w-3 h-3" />
                  </button>
                )}

                <button
                  onClick={clearCurrentMusic}
                  className="btn btn-sm btn-circle btn-ghost text-base-content/60 hover:text-base-content ml-2"
                  title="Close player"
                >
                  <FaTimes className="w-3 h-3" />
                </button>
              </div>
            </div>

            {/* Time Display */}
            <div className="flex justify-between text-xs mt-1 text-base-content/60">
              <span>
                {audioRef.current ? formatTime(audioRef.current.currentTime) : '0:00'}
              </span>
              <span>
                {duration ? formatTime(duration) : '0:00'}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
