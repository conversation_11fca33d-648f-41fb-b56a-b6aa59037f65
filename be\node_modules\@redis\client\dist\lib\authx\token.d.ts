/**
 * A token that can be used to authenticate with a service.
 */
export declare class Token<T> {
    readonly value: T;
    readonly expiresAtMs: number;
    readonly receivedAtMs: number;
    constructor(value: T, expiresAtMs: number, receivedAtMs: number);
    /**
     * Returns the time-to-live of the token in milliseconds.
     * @param now The current time in milliseconds since the Unix epoch.
     */
    getTtlMs(now: number): number;
}
//# sourceMappingURL=token.d.ts.map