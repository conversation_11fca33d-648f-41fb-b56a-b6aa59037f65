import { useState, useEffect, useCallback, useRef, useMemo } from "react";
import { musicService } from "../services/api";
import { useMusicPlayer } from "../contexts/MusicPlayerContext";
import { API_URL } from "../config/api";
import DropdownIcon from "../components/DropdownIcon";
import defaultCoverImage from "../assets/wood-blog-placeholder.jpg";
import { useNavigate } from "react-router-dom";
import { Music } from "../types";

interface MusicPlayerProps {
  audioUrl: string;
  onVersionChange?: (version: number) => void;
  versions?: number;
  currentVersion?: number;
}

const MusicPlayer = ({
  audioUrl,
  onVersionChange,
  versions = 1,
  currentVersion = 1,
}: MusicPlayerProps) => {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleLoadedMetadata = () => {
      setDuration(audio.duration);
    };

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setCurrentTime(0);
    };

    audio.addEventListener("loadedmetadata", handleLoadedMetadata);
    audio.addEventListener("timeupdate", handleTimeUpdate);
    audio.addEventListener("ended", handleEnded);

    return () => {
      audio.removeEventListener("loadedmetadata", handleLoadedMetadata);
      audio.removeEventListener("timeupdate", handleTimeUpdate);
      audio.removeEventListener("ended", handleEnded);
    };
  }, []);

  const togglePlay = () => {
    if (!audioRef.current) return;

    if (isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!audioRef.current) return;
    const time = Number(e.target.value);
    audioRef.current.currentTime = time;
    setCurrentTime(time);
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!audioRef.current) return;
    const vol = Number(e.target.value);
    audioRef.current.volume = vol;
    setVolume(vol);
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  return (
    <div className="bg-base-200 rounded-xl p-4">
      <audio ref={audioRef} src={audioUrl} preload="metadata" />

      <div className="mb-4">
        <input
          type="range"
          min={0}
          max={duration}
          value={currentTime}
          onChange={handleSeek}
          className="range range-xs range-primary w-full"
          step="any"
        />
        <div className="flex justify-between text-xs text-base-content/60 mt-1">
          <span>{formatTime(currentTime)}</span>
          <span>{formatTime(duration)}</span>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <button
            onClick={togglePlay}
            className="btn btn-circle btn-primary btn-sm"
          >
            {isPlaying ? (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                  clipRule="evenodd"
                />
              </svg>
            )}
          </button>

          <div className="flex items-center gap-2">
            <button
              onClick={() => {
                if (!audioRef.current) return;
                const newVolume = volume === 0 ? 1 : 0;
                audioRef.current.volume = newVolume;
                setVolume(newVolume);
              }}
              className="btn btn-ghost btn-circle btn-sm"
            >
              {volume === 0 ? (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              ) : (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M14.657 2.929a1 1 0 011.414 0A9.972 9.972 0 0119 10a9.972 9.972 0 01-2.929 7.071 1 1 0 01-1.414-1.414A7.971 7.971 0 0117 10c0-2.21-.894-4.208-2.343-5.657a1 1 0 010-1.414zm-2.829 2.828a1 1 0 011.415 0A5.983 5.983 0 0115 10a5.984 5.984 0 01-1.757 4.243 1 1 0 01-1.415-1.415A3.984 3.984 0 0013 10a3.983 3.983 0 00-1.172-2.828 1 1 0 010-1.415z"
                    clipRule="evenodd"
                  />
                </svg>
              )}
            </button>
            <input
              type="range"
              min={0}
              max={1}
              value={volume}
              onChange={handleVolumeChange}
              step="any"
              className="range range-xs range-primary w-20"
            />
          </div>
        </div>

        <div className="flex items-center gap-2">
          {versions > 1 && (
            <select
              onChange={(e) => onVersionChange?.(Number(e.target.value))}
              className="select select-bordered select-xs"
              value={currentVersion}
            >
              {Array.from({ length: versions }, (_, i) => i + 1).map((v) => (
                <option key={v} value={v}>
                  Versi {v}
                </option>
              ))}
            </select>
          )}
          <select
            onChange={(e) => {
              if (!audioRef.current) return;
              audioRef.current.playbackRate = Number(e.target.value);
            }}
            className="select select-bordered select-xs"
            defaultValue="1"
          >
            <option value="0.5">0.5x</option>
            <option value="0.75">0.75x</option>
            <option value="1">1x</option>
            <option value="1.25">1.25x</option>
            <option value="1.5">1.5x</option>
            <option value="2">2x</option>
          </select>
        </div>
      </div>
    </div>
  );
};

export default function History() {
  const { setCurrentMusic, setIsPlaying, setCurrentVersion } = useMusicPlayer();
  const [musics, setMusics] = useState<Music[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedMusic, setSelectedMusic] = useState<Music | null>(null);
  const [showLyricsModal, setShowLyricsModal] = useState(false);
  const [selectedLyrics, setSelectedLyrics] = useState<string>("");
  const [selectedTitle, setSelectedTitle] = useState<string>("");
  const [pollingIds, setPollingIds] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [isCopied, setIsCopied] = useState(false);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [sortBy, setSortBy] = useState<
    "newest" | "oldest" | "title" | "status"
  >("newest");
  const [openDropdownIndex, setOpenDropdownIndex] = useState<string | null>(
    null
  );

  const localUser = localStorage.getItem("user");
  const currentUser = localUser ? JSON.parse(localUser) : null;
  const currentUserId: string | null = currentUser ? currentUser._id : null;

  const navigate = useNavigate();

  const toggleDropdown = (id: string) => {
    setOpenDropdownIndex(openDropdownIndex === id ? null : id);
  };

  const handleCopyLyrics = async () => {
    try {
      await navigator.clipboard.writeText(selectedLyrics);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy lyrics:", err);
    }
  };

  type MusicStatus = "completed" | "failed" | "pending";

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "badge-success";
      case "pending":
        return "badge-warning";
      case "failed":
        return "badge-error";
      default:
        return "badge-ghost";
    }
  };

  const getStatusDisplay = (status: string) => {
    return status === "me"
      ? "Musik Saya"
      : status === "other"
      ? "Orang Lain"
      : "Semua";
  };

  const filteredAndSortedMusics = useMemo((): Music[] => {
    const filtered = musics.filter((music: Music) => {
      if (music.visibility !== "public") return false;

      const matchesSearch =
        music.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        music.prompt.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (music.tags &&
          music.tags.toLowerCase().includes(searchQuery.toLowerCase()));

      let matchesStatus = false;
      if (statusFilter === "me") {
        matchesStatus = music.userId?._id === currentUserId;
      } else if (statusFilter === "other") {
        matchesStatus = music.userId?._id !== currentUserId;
      } else if (statusFilter === "all") {
        matchesStatus = true;
      }
      return matchesSearch && matchesStatus;
    });

    return filtered.sort((a: Music, b: Music) => {
      switch (sortBy) {
        case "newest":
          return (
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
        case "oldest":
          return (
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          );
        case "title":
          return a.title.localeCompare(b.title);
        case "status":
          return a.status.localeCompare(b.status);
        default:
          return 0;
      }
    });
  }, [musics, searchQuery, statusFilter, sortBy, currentUserId]);

  const stats = {
    total: musics.filter((m) => m.visibility === "public").length,
    my_musics: musics.filter(
      (m) => m.visibility === "public" && m.userId?._id === currentUserId
    ).length,
    shared_musics: musics.filter(
      (m) => m.visibility === "public" && m.userId?._id !== currentUserId
    ).length,
  };

  const loadMusics = async (): Promise<void> => {
    try {
      const data = await musicService.listMusic();
      const musicMap = new Map<string, Music>();

      data.data.userMusics.forEach((music: Music) => {
        if (music.visibility === "public") {
          const type: "me" | "other" =
            music.userId?._id === currentUserId ? "me" : "other";
          musicMap.set(music._id, { ...music, type });
        }
      });

      data.data.sharedMusics.forEach((music: Music) => {
        if (music.visibility === "public" && !musicMap.has(music._id)) {
          musicMap.set(music._id, { ...music, type: "other" });
        }
      });

      setMusics(Array.from(musicMap.values()));
    } catch (error) {
      console.error("Error loading music:", error);
    } finally {
      setLoading(false);
    }
  };

  const pollMusicStatus = useCallback(
    async (id: string) => {
      try {
        const updatedMusic = await musicService.getMusic(id);

        setMusics((prevMusics) =>
          prevMusics.map((music) => (music._id === id ? updatedMusic : music))
        );

        if (selectedMusic?._id === id) {
          setSelectedMusic(updatedMusic);
        }

        if (updatedMusic.status !== "pending") {
          setPollingIds((prev) => {
            const next = new Set(prev);
            next.delete(id);
            return next;
          });
        }
      } catch (error) {
        console.error(`Error polling music ${id}:`, error);
      }
    },
    [selectedMusic]
  );

  useEffect(() => {
    loadMusics();
  }, []);

  useEffect(() => {
    if (pollingIds.size === 0) return;

    const intervals: NodeJS.Timeout[] = [];

    pollingIds.forEach((id) => {
      const interval = setInterval(() => {
        pollMusicStatus(id);
      }, 5000);
      intervals.push(interval);
    });

    return () => {
      intervals.forEach(clearInterval);
    };
  }, [pollingIds, pollMusicStatus]);

  const handlePlayClick = (music: Music, version: number = 1) => {
    // Check tracks first, then audioUrl
    if (!music.tracks && !music.audioUrl) return;
    setCurrentMusic(music);
    setCurrentVersion(version);
    setIsPlaying(true);
  };

  const handleDownload = async (music: Music, version: number = 1) => {
    try {
      let audioUrl = "";
      // Check tracks first, then audioUrl
      if (music.tracks && music.tracks.length > 0) {
        audioUrl = music.tracks[version - 1]?.audioUrl || "";
      } else {
        audioUrl = music.audioUrl || "";
      }

      if (!audioUrl) {
        console.error("No audio URL provided");
        return;
      }

      const fullUrl = audioUrl.startsWith("http")
        ? audioUrl
        : `${API_URL}${audioUrl}`;
      console.log("Downloading from:", fullUrl);

      const response = await fetch(fullUrl);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = `${music.title} - Versi ${version}.mp3`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error("Error downloading file:", error);
    }
  };

  const renderMusicCard = (music: Music) => {
    const hasMultipleVersions = music.clips && music.clips.length > 1;
    const currentClip = music.clips?.[0];

    return (
      <div
        key={music._id}
        className={`bg-base-100 border border-base-200 rounded-xl transition-all duration-300 hover:shadow-lg hover:border-primary/20 ${
          viewMode === "list"
            ? "flex flex-col md:flex-row md:items-center md:gap-6"
            : ""
        }`}
      >
        <div className={viewMode === "list" ? "flex-1" : ""}>
          <div className="relative group">
            <img
              src={music.cover ?? defaultCoverImage}
              alt="Music Cover"
              className="w-full h-48 object-cover rounded-t-lg"
            />
            <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
              <button
                className="btn btn-circle btn-primary btn-sm"
                onClick={() => navigate(`/music/${music._id}`)}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="w-5 h-5"
                >
                  <polygon points="5 3 19 12 5 21 5 3"></polygon>
                </svg>
              </button>
            </div>
          </div>
          <div className="p-5">
            <div className="flex items-start justify-between pb-3">
              <div className="pr-4">
                <h3 className="text-lg font-medium mb-1">{music.title}</h3>
                <p className="text-sm text-base-content/60 line-clamp-2 mb-3">
                  {music.prompt}
                </p>
              </div>
              <DropdownIcon
                isOpen={openDropdownIndex === music._id}
                toggleDropdown={() => toggleDropdown(music._id)}
                musicId={music._id}
                musicType={(music as any).type}
                variant="list"
              />
            </div>

            <div className="flex items-center gap-2 mt-2">
              <div className="flex items-center gap-2">
                <div className="avatar">
                  <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 text-primary"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                </div>
                <div>
                  <p className="text-xs font-medium">
                    {music.userId?.name ?? "Unknown User"}
                  </p>
                  <p className="text-xs text-base-content/60">
                    {new Date(music.createdAt).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <span className="loading loading-spinner loading-lg"></span>
      </div>
    );
  }

  return (
    <div className="container-custom p-4 lg:p-8 max-w-7xl mx-auto">
      <div className="mb-6 lg:mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center gap-4 mb-6">
          <div className="flex items-center gap-3">
            <div className="p-3 sm:p-4 bg-primary/10 rounded-lg">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-7 w-7 sm:h-8 sm:w-8 text-primary"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v9.114A4.369 4.369 0 005 14c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V7.82l8-1.6v5.894A4.37 4.37 0 0015 12c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V3z" />
              </svg>
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold">Explore Musik</h1>
              <p className="text-sm sm:text-base text-base-content/60">
                Temukan dan dengarkan musik yang telah dibuat dan dibagikan oleh komunitas.
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 mb-6">
          <div className="bg-base-100 border border-base-200 rounded-xl p-4 sm:p-5 shadow-sm hover:shadow-md transition-all duration-300">
            <div className="text-xs sm:text-sm text-base-content/60 mb-1">
              Total Musik
            </div>
            <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-primary">
              {stats.total}
            </div>
          </div>
          <div className="bg-base-100 border border-base-200 rounded-xl p-4 sm:p-5 shadow-sm hover:shadow-md transition-all duration-300">
            <div className="text-xs sm:text-sm text-base-content/60 mb-1">
              Musik Saya
            </div>
            <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-success">
              {stats.my_musics}
            </div>
          </div>
          <div className="bg-base-100 border border-base-200 rounded-xl p-4 sm:p-5 shadow-sm hover:shadow-md transition-all duration-300">
            <div className="text-xs sm:text-sm text-base-content/60 mb-1">
              Musik Publik
            </div>
            <div className="text-xl sm:text-2xl lg:text-3xl font-bold text-warning">
              {stats.shared_musics}
            </div>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 mb-8">
          <div className="flex-1">
            <div className="relative">
              <input
                type="text"
                placeholder="Cari berdasarkan judul, prompt, atau tag..."
                className="input input-bordered w-full pl-10 bg-base-100 text-sm focus:border-primary focus:ring-1 focus:ring-primary"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 absolute left-3 top-1/2 -translate-y-1/2 text-base-content/40"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
          </div>
          <div className="flex gap-2 flex-col sm:flex-row">
            <select
              className="select select-bordered w-full sm:w-48 bg-base-100 text-sm focus:border-primary"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">Semua</option>
              <option value="me">Musik Saya</option>
              <option value="other">Musik Orang Lain</option>
            </select>
            <select
              className="select select-bordered w-full sm:w-48 bg-base-100 text-sm focus:border-primary"
              value={sortBy}
              onChange={(e) =>
                setSortBy(
                  e.target.value as "newest" | "oldest" | "title" | "status"
                )
              }
            >
              <option value="newest">Terbaru</option>
              <option value="oldest">Terlama</option>
              <option value="title">Judul A-Z</option>
              <option value="status">Status</option>
            </select>
            <div className="join bg-base-100 border border-base-300 rounded-lg">
              <button
                className={`btn btn-sm join-item ${
                  viewMode === "grid" ? "btn-primary" : "btn-ghost"
                }`}
                onClick={() => setViewMode("grid")}
                title="Grid View"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                  />
                </svg>
              </button>
              <button
                className={`btn btn-sm join-item ${
                  viewMode === "list" ? "btn-primary" : "btn-ghost"
                }`}
                onClick={() => setViewMode("list")}
                title="List View"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <div
          className={`mt-6 ${
            viewMode === "grid"
              ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
              : "space-y-6"
          }`}
        >
          {filteredAndSortedMusics.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-[300px] sm:h-[400px] bg-base-100 border border-base-200 rounded-xl p-6 col-span-full">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-16 w-16 text-base-content/30 mb-4"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM7 9a1 1 0 000 2h6a1 1 0 100-2H7z"
                  clipRule="evenodd"
                />
              </svg>
              <p className="text-base text-base-content/60 text-center max-w-md">
                Belum ada musik yang dibuat dan dibagikan. Mulai buat musik baru untuk ditampilkan di sini.
              </p>
            </div>
          ) : (
            filteredAndSortedMusics.map((music) => renderMusicCard(music))
          )}
        </div>

        {showLyricsModal && (
          <div className="fixed inset-0 z-40">
            <div className="flex items-start justify-center h-[calc(100vh-80px)] p-4">
              <div className="fixed inset-0 bg-black/60 backdrop-blur-sm transition-opacity" />
              <div className="relative bg-base-100 rounded-2xl max-w-lg w-full shadow-2xl mt-8 border border-base-200">
                <div className="flex items-center justify-between px-6 py-4 border-b border-base-200 bg-base-100/50 backdrop-blur supports-[backdrop-filter]:bg-base-100/50 rounded-t-2xl">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 text-primary"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v9.114A4.369 4.369 0 005 14c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V7.82l8-1.6v5.894A4.37 4.37 0 0015 12c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V3z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold leading-none mb-1">
                        {selectedTitle}
                      </h3>
                      <p className="text-xs text-base-content/60">Lirik Lagu</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={handleCopyLyrics}
                      className="btn btn-ghost btn-sm gap-2 hover:bg-base-200 transition-colors"
                      title="Salin lirik"
                    >
                      {isCopied ? (
                        <>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 text-success"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                          <span className="text-success">Tersalin!</span>
                        </>
                      ) : (
                        <>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path d="M7 9a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H9a2 2 0 01-2-2V9z" />
                            <path d="M5 3a2 2 0 00-2 2v6a2 2 0 110 4v5a2 2 0 002 2h14a2 2 0 002-2v-5a2 2 0 110-4V5a2 2 0 00-2-2H5z" />
                          </svg>
                          <span>Salin</span>
                        </>
                      )}
                    </button>
                    <button
                      onClick={() => setShowLyricsModal(false)}
                      className="btn btn-ghost btn-sm btn-square hover:bg-base-200 hover:text-error transition-colors"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </button>
                  </div>
                </div>

                <div
                  className="p-6 overflow-y-auto bg-base-100 rounded-b-2xl"
                  style={{ maxHeight: "calc(100vh - 250px)" }}
                >
                  <div className="prose prose-sm max-w-none">
                    <div className="bg-base-200/50 rounded-xl p-6">
                      <p className="whitespace-pre-wrap text-base-content/90 leading-relaxed">
                        {selectedLyrics}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
