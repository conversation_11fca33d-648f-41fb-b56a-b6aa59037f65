import { CommandParser } from '../client/parser';
import { SimpleStringReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: false;
    /**
     * Swaps the data of two Redis databases.
     * @param parser - The Redis command parser.
     * @param index1 - First database index.
     * @param index2 - Second database index.
     */
    readonly parseCommand: (this: void, parser: CommandParser, index1: number, index2: number) => void;
    readonly transformReply: () => SimpleStringReply<'OK'>;
};
export default _default;
//# sourceMappingURL=SWAPDB.d.ts.map