import { CommandParser } from '../client/parser';
import { RedisArgument, SimpleStringReply } from '../RESP/types';
declare const _default: {
    /**
     * Constructs the SETEX command
     *
     * @param parser - The command parser
     * @param key - The key to set
     * @param seconds - The expiration time in seconds
     * @param value - The value to set
     * @see https://redis.io/commands/setex/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, seconds: number, value: RedisArgument) => void;
    readonly transformReply: () => SimpleStringReply<'OK'>;
};
export default _default;
//# sourceMappingURL=SETEX.d.ts.map