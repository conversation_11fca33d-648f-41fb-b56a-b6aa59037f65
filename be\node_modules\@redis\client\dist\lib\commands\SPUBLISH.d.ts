import { CommandParser } from '../client/parser';
import { RedisArgument, NumberReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: true;
    /**
     * Constructs the SPUBLISH command to post a message to a Sharded Pub/Sub channel
     *
     * @param parser - The command parser
     * @param channel - The channel to publish to
     * @param message - The message to publish
     * @see https://redis.io/commands/spublish/
     */
    readonly parseCommand: (this: void, parser: CommandParser, channel: RedisArgument, message: RedisArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=SPUBLISH.d.ts.map