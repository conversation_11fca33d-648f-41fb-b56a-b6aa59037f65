"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PollingService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PollingService = void 0;
const common_1 = require("@nestjs/common");
const bull_1 = require("@nestjs/bull");
const constant_1 = require("./constant");
let PollingService = PollingService_1 = class PollingService {
    constructor(pollingQueue) {
        this.pollingQueue = pollingQueue;
        this.logger = new common_1.Logger(PollingService_1.name);
    }
    async startPollingMusicStatus(data) {
        this.logger.log(`Starting polling music task for taskId: ${data.taskId}`);
        await this.pollingQueue.add(constant_1.QUEUE_POLLING_MUSIC_STATUS_JOB, data, {
            jobId: data.taskId,
            repeat: {
                every: constant_1.QUEUE_POLLING_INTERVAL
            },
            removeOnComplete: true
        });
    }
    async stopPollingMusicStatus(taskId) {
        this.logger.log(`Stopping polling music task for taskId: ${taskId}`);
        await this.pollingQueue.removeRepeatable(constant_1.QUEUE_POLLING_MUSIC_STATUS_JOB, {
            every: constant_1.QUEUE_POLLING_INTERVAL,
            jobId: taskId
        });
    }
    async startPollingCoverStatus(data) {
        this.logger.log(`Starting polling cover status for taskId: ${data.taskId}`);
        await this.pollingQueue.add(constant_1.QUEUE_POLLING_COVER_STATUS_JOB, data, {
            jobId: data.taskId,
            repeat: {
                every: constant_1.QUEUE_POLLING_INTERVAL
            },
            removeOnComplete: true
        });
    }
    async stopPollingCoverStatus(taskId) {
        this.logger.log(`Stopping polling cover status for taskId: ${taskId}`);
        await this.pollingQueue.removeRepeatable(constant_1.QUEUE_POLLING_COVER_STATUS_JOB, {
            every: constant_1.QUEUE_POLLING_INTERVAL,
            jobId: taskId
        });
    }
};
exports.PollingService = PollingService;
exports.PollingService = PollingService = PollingService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, bull_1.InjectQueue)(constant_1.QUEUE_POLLING)),
    __metadata("design:paramtypes", [Object])
], PollingService);
//# sourceMappingURL=polling.service.js.map